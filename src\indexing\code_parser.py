"""
TreeSitter-based code parser for multi-language support
"""
import os
from pathlib import Path
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass
from enum import Enum

try:
    import tree_sitter_python as tspython
    import tree_sitter_javascript as tsjavascript
    import tree_sitter_typescript as tstypescript
    import tree_sitter_java as tsjava
    import tree_sitter_cpp as tscpp
    import tree_sitter_c as tsc
    import tree_sitter_rust as tsrust
    import tree_sitter_go as tsgo
    from tree_sitter import Language, Parser, Node
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False

from src.config.logging_config import get_logger

logger = get_logger(__name__)


class LanguageType(Enum):
    """Supported programming languages"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    CPP = "cpp"
    C = "c"
    RUST = "rust"
    GO = "go"


@dataclass
class CodeNode:
    """Represents a parsed code node"""
    type: str
    name: Optional[str]
    start_line: int
    end_line: int
    start_byte: int
    end_byte: int
    text: str
    children: List['CodeNode']
    metadata: Dict[str, Any]


@dataclass
class DependencyInfo:
    """Information about code dependencies"""
    imports: List[str]
    function_calls: List[str]
    class_references: List[str]
    variable_references: List[str]


@dataclass
class SymbolInfo:
    """Information about a code symbol"""
    name: str
    type: str  # function, class, variable, etc.
    file_path: str
    start_line: int
    end_line: int
    scope: str
    signature: Optional[str] = None
    docstring: Optional[str] = None


class CodeParser:
    """Multi-language code parser using TreeSitter"""
    
    def __init__(self):
        self.parsers: Dict[LanguageType, Parser] = {}
        self.languages: Dict[LanguageType, Language] = {}
        self._initialize_parsers()
    
    def _initialize_parsers(self):
        """Initialize TreeSitter parsers for supported languages"""
        if not TREE_SITTER_AVAILABLE:
            logger.warning("TreeSitter not available. Code parsing will be limited.")
            return
        
        language_configs = {
            LanguageType.PYTHON: tspython.language(),
            LanguageType.JAVASCRIPT: tsjavascript.language(),
            LanguageType.TYPESCRIPT: tstypescript.language(),
            LanguageType.JAVA: tsjava.language(),
            LanguageType.CPP: tscpp.language(),
            LanguageType.C: tsc.language(),
            LanguageType.RUST: tsrust.language(),
            LanguageType.GO: tsgo.language(),
        }
        
        for lang_type, language_func in language_configs.items():
            try:
                language = Language(language_func)
                parser = Parser(language)
                
                self.languages[lang_type] = language
                self.parsers[lang_type] = parser
                
                logger.debug(f"Initialized parser for {lang_type.value}")
            except Exception as e:
                logger.warning(f"Failed to initialize parser for {lang_type.value}: {e}")
    
    def detect_language(self, file_path: str) -> Optional[LanguageType]:
        """Detect programming language from file extension"""
        extension = Path(file_path).suffix.lower()
        
        extension_map = {
            '.py': LanguageType.PYTHON,
            '.js': LanguageType.JAVASCRIPT,
            '.jsx': LanguageType.JAVASCRIPT,
            '.ts': LanguageType.TYPESCRIPT,
            '.tsx': LanguageType.TYPESCRIPT,
            '.java': LanguageType.JAVA,
            '.cpp': LanguageType.CPP,
            '.cc': LanguageType.CPP,
            '.cxx': LanguageType.CPP,
            '.c': LanguageType.C,
            '.h': LanguageType.C,
            '.hpp': LanguageType.CPP,
            '.rs': LanguageType.RUST,
            '.go': LanguageType.GO,
        }
        
        return extension_map.get(extension)
    
    def parse_code(self, code: str, language: LanguageType) -> Optional[CodeNode]:
        """Parse code and return AST"""
        if not TREE_SITTER_AVAILABLE or language not in self.parsers:
            logger.warning(f"Parser not available for {language.value}")
            return None
        
        try:
            parser = self.parsers[language]
            tree = parser.parse(bytes(code, "utf8"))
            
            return self._convert_node(tree.root_node, code)
        except Exception as e:
            logger.error(f"Failed to parse code: {e}")
            return None
    
    def _convert_node(self, node: 'Node', code: str) -> CodeNode:
        """Convert TreeSitter node to CodeNode"""
        children = []
        for child in node.children:
            children.append(self._convert_node(child, code))
        
        # Extract node text
        start_byte = node.start_byte
        end_byte = node.end_byte
        text = code[start_byte:end_byte] if start_byte < len(code) else ""
        
        # Extract name if available
        name = None
        if hasattr(node, 'child_by_field_name'):
            name_node = node.child_by_field_name('name')
            if name_node:
                name = code[name_node.start_byte:name_node.end_byte]
        
        return CodeNode(
            type=node.type,
            name=name,
            start_line=node.start_point[0] + 1,  # Convert to 1-based
            end_line=node.end_point[0] + 1,
            start_byte=start_byte,
            end_byte=end_byte,
            text=text,
            children=children,
            metadata={}
        )
    
    def extract_functions(self, root_node: CodeNode, language: LanguageType) -> List[CodeNode]:
        """Extract function definitions from AST"""
        functions = []
        
        function_types = {
            LanguageType.PYTHON: ['function_definition', 'async_function_definition'],
            LanguageType.JAVASCRIPT: ['function_declaration', 'function_expression', 'arrow_function'],
            LanguageType.TYPESCRIPT: ['function_declaration', 'function_expression', 'arrow_function', 'method_definition'],
            LanguageType.JAVA: ['method_declaration', 'constructor_declaration'],
            LanguageType.CPP: ['function_definition', 'function_declarator'],
            LanguageType.C: ['function_definition', 'function_declarator'],
            LanguageType.RUST: ['function_item'],
            LanguageType.GO: ['function_declaration', 'method_declaration'],
        }
        
        target_types = function_types.get(language, [])
        self._collect_nodes_by_type(root_node, target_types, functions)
        
        return functions
    
    def extract_classes(self, root_node: CodeNode, language: LanguageType) -> List[CodeNode]:
        """Extract class definitions from AST"""
        classes = []
        
        class_types = {
            LanguageType.PYTHON: ['class_definition'],
            LanguageType.JAVASCRIPT: ['class_declaration'],
            LanguageType.TYPESCRIPT: ['class_declaration', 'interface_declaration'],
            LanguageType.JAVA: ['class_declaration', 'interface_declaration'],
            LanguageType.CPP: ['class_specifier', 'struct_specifier'],
            LanguageType.C: ['struct_specifier'],
            LanguageType.RUST: ['struct_item', 'enum_item', 'trait_item', 'impl_item'],
            LanguageType.GO: ['type_declaration'],
        }
        
        target_types = class_types.get(language, [])
        self._collect_nodes_by_type(root_node, target_types, classes)
        
        return classes
    
    def extract_imports(self, root_node: CodeNode, language: LanguageType) -> List[str]:
        """Extract import statements from AST"""
        imports = []
        
        import_types = {
            LanguageType.PYTHON: ['import_statement', 'import_from_statement'],
            LanguageType.JAVASCRIPT: ['import_statement'],
            LanguageType.TYPESCRIPT: ['import_statement'],
            LanguageType.JAVA: ['import_declaration'],
            LanguageType.CPP: ['preproc_include'],
            LanguageType.C: ['preproc_include'],
            LanguageType.RUST: ['use_declaration'],
            LanguageType.GO: ['import_declaration'],
        }
        
        target_types = import_types.get(language, [])
        import_nodes = []
        self._collect_nodes_by_type(root_node, target_types, import_nodes)
        
        for node in import_nodes:
            imports.append(node.text.strip())
        
        return imports
    
    def _collect_nodes_by_type(self, node: CodeNode, target_types: List[str], result: List[CodeNode]):
        """Recursively collect nodes of specific types"""
        if node.type in target_types:
            result.append(node)
        
        for child in node.children:
            self._collect_nodes_by_type(child, target_types, result)
    
    def build_symbol_table(self, root_node: CodeNode, file_path: str, language: LanguageType) -> List[SymbolInfo]:
        """Build symbol table from AST"""
        symbols = []
        
        # Extract functions
        functions = self.extract_functions(root_node, language)
        for func in functions:
            symbols.append(SymbolInfo(
                name=func.name or "anonymous",
                type="function",
                file_path=file_path,
                start_line=func.start_line,
                end_line=func.end_line,
                scope="global",  # TODO: Implement proper scope detection
                signature=self._extract_function_signature(func, language),
                docstring=self._extract_docstring(func, language)
            ))
        
        # Extract classes
        classes = self.extract_classes(root_node, language)
        for cls in classes:
            symbols.append(SymbolInfo(
                name=cls.name or "anonymous",
                type="class",
                file_path=file_path,
                start_line=cls.start_line,
                end_line=cls.end_line,
                scope="global",
                signature=None,
                docstring=self._extract_docstring(cls, language)
            ))
        
        return symbols
    
    def _extract_function_signature(self, func_node: CodeNode, language: LanguageType) -> Optional[str]:
        """Extract function signature"""
        # This is a simplified implementation
        # In a full implementation, we would parse parameters, return types, etc.
        return func_node.text.split('\n')[0].strip() if func_node.text else None
    
    def _extract_docstring(self, node: CodeNode, language: LanguageType) -> Optional[str]:
        """Extract docstring from node"""
        # This is a simplified implementation
        # In a full implementation, we would look for specific docstring patterns
        return None
    
    def analyze_dependencies(self, root_node: CodeNode, language: LanguageType) -> DependencyInfo:
        """Analyze code dependencies"""
        imports = self.extract_imports(root_node, language)
        
        # TODO: Implement function call and reference extraction
        function_calls = []
        class_references = []
        variable_references = []
        
        return DependencyInfo(
            imports=imports,
            function_calls=function_calls,
            class_references=class_references,
            variable_references=variable_references
        )

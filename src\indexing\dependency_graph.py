"""
Dependency graph builder for codebase analysis
"""
from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path
import networkx as nx

from src.indexing.code_parser import CodeParser, LanguageType, DependencyInfo, SymbolInfo
from src.config.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class FileNode:
    """Represents a file in the dependency graph"""
    path: str
    language: Optional[LanguageType]
    symbols: List[SymbolInfo] = field(default_factory=list)
    dependencies: DependencyInfo = field(default_factory=lambda: DependencyInfo([], [], [], []))
    size: int = 0
    last_modified: Optional[float] = None


@dataclass
class DependencyEdge:
    """Represents a dependency relationship"""
    source: str
    target: str
    dependency_type: str  # import, function_call, class_reference, etc.
    strength: float = 1.0  # Dependency strength (0.0 to 1.0)


class DependencyGraphBuilder:
    """Builds and manages dependency graphs for codebases"""
    
    def __init__(self, code_parser: CodeParser):
        self.code_parser = code_parser
        self.graph = nx.DiGraph()
        self.file_nodes: Dict[str, FileNode] = {}
        self.symbol_index: Dict[str, List[SymbolInfo]] = {}  # symbol_name -> [SymbolInfo]
    
    def add_file(self, file_path: str, code: str) -> bool:
        """Add a file to the dependency graph"""
        try:
            # Detect language
            language = self.code_parser.detect_language(file_path)
            if not language:
                logger.debug(f"Unsupported language for file: {file_path}")
                return False
            
            # Parse code
            root_node = self.code_parser.parse_code(code, language)
            if not root_node:
                logger.warning(f"Failed to parse file: {file_path}")
                return False
            
            # Extract symbols and dependencies
            symbols = self.code_parser.build_symbol_table(root_node, file_path, language)
            dependencies = self.code_parser.analyze_dependencies(root_node, language)
            
            # Create file node
            file_node = FileNode(
                path=file_path,
                language=language,
                symbols=symbols,
                dependencies=dependencies,
                size=len(code),
                last_modified=None  # TODO: Get actual modification time
            )
            
            # Add to graph
            self.file_nodes[file_path] = file_node
            self.graph.add_node(file_path, **file_node.__dict__)
            
            # Update symbol index
            for symbol in symbols:
                if symbol.name not in self.symbol_index:
                    self.symbol_index[symbol.name] = []
                self.symbol_index[symbol.name].append(symbol)
            
            logger.debug(f"Added file to dependency graph: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add file to dependency graph: {file_path}, error: {e}")
            return False
    
    def build_dependencies(self):
        """Build dependency edges between files"""
        for file_path, file_node in self.file_nodes.items():
            self._build_import_dependencies(file_path, file_node)
            self._build_symbol_dependencies(file_path, file_node)
    
    def _build_import_dependencies(self, file_path: str, file_node: FileNode):
        """Build dependencies based on import statements"""
        for import_stmt in file_node.dependencies.imports:
            target_files = self._resolve_import(import_stmt, file_path, file_node.language)
            
            for target_file in target_files:
                if target_file in self.file_nodes:
                    self.graph.add_edge(
                        file_path, 
                        target_file,
                        dependency_type="import",
                        strength=1.0,
                        import_statement=import_stmt
                    )
    
    def _build_symbol_dependencies(self, file_path: str, file_node: FileNode):
        """Build dependencies based on symbol usage"""
        # TODO: Implement symbol usage analysis
        # This would involve analyzing function calls, class instantiations, etc.
        pass
    
    def _resolve_import(self, import_stmt: str, current_file: str, language: Optional[LanguageType]) -> List[str]:
        """Resolve import statement to actual file paths"""
        resolved_files = []
        
        if not language:
            return resolved_files
        
        try:
            if language == LanguageType.PYTHON:
                resolved_files.extend(self._resolve_python_import(import_stmt, current_file))
            elif language in [LanguageType.JAVASCRIPT, LanguageType.TYPESCRIPT]:
                resolved_files.extend(self._resolve_js_import(import_stmt, current_file))
            elif language == LanguageType.JAVA:
                resolved_files.extend(self._resolve_java_import(import_stmt, current_file))
            # Add more language-specific resolvers as needed
            
        except Exception as e:
            logger.debug(f"Failed to resolve import '{import_stmt}' in {current_file}: {e}")
        
        return resolved_files
    
    def _resolve_python_import(self, import_stmt: str, current_file: str) -> List[str]:
        """Resolve Python import statements"""
        resolved = []
        current_dir = Path(current_file).parent
        
        # Simple heuristic-based resolution
        # In a full implementation, this would be much more sophisticated
        
        if import_stmt.startswith('from '):
            # from module import something
            parts = import_stmt.split()
            if len(parts) >= 4:
                module_name = parts[1]
                module_path = self._python_module_to_path(module_name, current_dir)
                if module_path:
                    resolved.append(module_path)
        elif import_stmt.startswith('import '):
            # import module
            module_name = import_stmt.replace('import ', '').split('.')[0].strip()
            module_path = self._python_module_to_path(module_name, current_dir)
            if module_path:
                resolved.append(module_path)
        
        return resolved
    
    def _python_module_to_path(self, module_name: str, current_dir: Path) -> Optional[str]:
        """Convert Python module name to file path"""
        # Check relative imports
        possible_paths = [
            current_dir / f"{module_name}.py",
            current_dir / module_name / "__init__.py",
        ]
        
        for path in possible_paths:
            if path.exists() and str(path) in self.file_nodes:
                return str(path)
        
        return None
    
    def _resolve_js_import(self, import_stmt: str, current_file: str) -> List[str]:
        """Resolve JavaScript/TypeScript import statements"""
        # TODO: Implement JS/TS import resolution
        return []
    
    def _resolve_java_import(self, import_stmt: str, current_file: str) -> List[str]:
        """Resolve Java import statements"""
        # TODO: Implement Java import resolution
        return []
    
    def get_dependencies(self, file_path: str) -> List[str]:
        """Get direct dependencies of a file"""
        if file_path not in self.graph:
            return []
        
        return list(self.graph.successors(file_path))
    
    def get_dependents(self, file_path: str) -> List[str]:
        """Get files that depend on the given file"""
        if file_path not in self.graph:
            return []
        
        return list(self.graph.predecessors(file_path))
    
    def get_transitive_dependencies(self, file_path: str, max_depth: int = 5) -> Set[str]:
        """Get transitive dependencies up to max_depth"""
        if file_path not in self.graph:
            return set()
        
        dependencies = set()
        visited = set()
        queue = [(file_path, 0)]
        
        while queue:
            current_file, depth = queue.pop(0)
            
            if current_file in visited or depth >= max_depth:
                continue
            
            visited.add(current_file)
            
            for dependency in self.graph.successors(current_file):
                if dependency not in visited:
                    dependencies.add(dependency)
                    queue.append((dependency, depth + 1))
        
        return dependencies
    
    def find_circular_dependencies(self) -> List[List[str]]:
        """Find circular dependencies in the graph"""
        try:
            cycles = list(nx.simple_cycles(self.graph))
            return cycles
        except Exception as e:
            logger.error(f"Failed to find circular dependencies: {e}")
            return []
    
    def get_file_importance_score(self, file_path: str) -> float:
        """Calculate importance score based on dependencies"""
        if file_path not in self.graph:
            return 0.0
        
        # Simple scoring based on in-degree and out-degree
        in_degree = self.graph.in_degree(file_path)
        out_degree = self.graph.out_degree(file_path)
        
        # Files that are depended upon by many others are more important
        # Files that depend on many others are also somewhat important
        importance = in_degree * 2 + out_degree * 0.5
        
        return min(importance / 10.0, 1.0)  # Normalize to 0-1
    
    def get_related_files(self, file_path: str, max_files: int = 10) -> List[Tuple[str, float]]:
        """Get files related to the given file with relevance scores"""
        if file_path not in self.graph:
            return []
        
        related = []
        
        # Direct dependencies
        for dep in self.get_dependencies(file_path):
            related.append((dep, 0.9))
        
        # Direct dependents
        for dep in self.get_dependents(file_path):
            related.append((dep, 0.8))
        
        # Transitive dependencies (with lower scores)
        transitive = self.get_transitive_dependencies(file_path, max_depth=2)
        for dep in transitive:
            if dep not in [r[0] for r in related]:
                related.append((dep, 0.5))
        
        # Sort by relevance score and limit results
        related.sort(key=lambda x: x[1], reverse=True)
        return related[:max_files]
    
    def get_symbol_definitions(self, symbol_name: str) -> List[SymbolInfo]:
        """Get all definitions of a symbol"""
        return self.symbol_index.get(symbol_name, [])
    
    def get_file_symbols(self, file_path: str) -> List[SymbolInfo]:
        """Get all symbols defined in a file"""
        if file_path in self.file_nodes:
            return self.file_nodes[file_path].symbols
        return []
    
    def export_graph(self, format: str = "json") -> Dict:
        """Export dependency graph in specified format"""
        if format == "json":
            return {
                "nodes": [
                    {
                        "id": node,
                        "path": node,
                        "language": self.file_nodes[node].language.value if self.file_nodes[node].language else None,
                        "symbol_count": len(self.file_nodes[node].symbols),
                        "size": self.file_nodes[node].size,
                        "importance": self.get_file_importance_score(node)
                    }
                    for node in self.graph.nodes()
                ],
                "edges": [
                    {
                        "source": edge[0],
                        "target": edge[1],
                        "type": self.graph.edges[edge].get("dependency_type", "unknown"),
                        "strength": self.graph.edges[edge].get("strength", 1.0)
                    }
                    for edge in self.graph.edges()
                ]
            }
        
        return {}
    
    def get_statistics(self) -> Dict:
        """Get dependency graph statistics"""
        return {
            "total_files": len(self.file_nodes),
            "total_symbols": sum(len(node.symbols) for node in self.file_nodes.values()),
            "total_dependencies": self.graph.number_of_edges(),
            "languages": list(set(
                node.language.value for node in self.file_nodes.values() 
                if node.language
            )),
            "circular_dependencies": len(self.find_circular_dependencies()),
            "average_dependencies_per_file": (
                self.graph.number_of_edges() / len(self.file_nodes) 
                if self.file_nodes else 0
            )
        }

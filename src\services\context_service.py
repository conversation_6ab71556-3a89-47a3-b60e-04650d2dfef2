"""
Context service for intelligent code context assembly
"""
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from src.indexing.context_assembly import Context<PERSON>sembler, ContextRequest, ContextType, AssembledContext
from src.indexing.indexing_service import IndexingService
from src.services.file_service import FileService
from src.config.logging_config import get_logger

logger = get_logger(__name__)


class ContextService:
    """Service for assembling intelligent code context"""
    
    def __init__(self, db: Session):
        self.db = db
        self.indexing_service = IndexingService(db)
        self.file_service = FileService(db)
        self.context_assembler = ContextAssembler(self.indexing_service)
    
    async def get_context_for_file(
        self,
        project_id: int,
        file_path: str,
        max_tokens: int = 32000,
        include_dependencies: bool = True,
        include_similar: bool = False,
        query: Optional[str] = None
    ) -> AssembledContext:
        """Get context for a specific file"""
        
        include_types = [ContextType.CURRENT_FILE]
        
        if include_dependencies:
            include_types.extend([
                ContextType.DEPENDENCIES,
                ContextType.DEPENDENTS,
                ContextType.RELATED_SYMBOLS
            ])
        
        if include_similar and query:
            include_types.append(ContextType.SIMILAR_CODE)
        
        request = ContextRequest(
            project_id=project_id,
            focus_file=file_path,
            query=query,
            max_tokens=max_tokens,
            include_types=include_types
        )
        
        return await self.context_assembler.assemble_context(request)
    
    async def get_context_for_function(
        self,
        project_id: int,
        file_path: str,
        function_name: str,
        max_tokens: int = 16000,
        include_related: bool = True
    ) -> AssembledContext:
        """Get context for a specific function"""
        
        include_types = [ContextType.CURRENT_FILE]
        
        if include_related:
            include_types.extend([
                ContextType.DEPENDENCIES,
                ContextType.RELATED_SYMBOLS
            ])
        
        request = ContextRequest(
            project_id=project_id,
            focus_file=file_path,
            focus_function=function_name,
            max_tokens=max_tokens,
            include_types=include_types
        )
        
        return await self.context_assembler.assemble_context(request)
    
    async def get_context_for_class(
        self,
        project_id: int,
        file_path: str,
        class_name: str,
        max_tokens: int = 20000,
        include_related: bool = True
    ) -> AssembledContext:
        """Get context for a specific class"""
        
        include_types = [ContextType.CURRENT_FILE]
        
        if include_related:
            include_types.extend([
                ContextType.DEPENDENCIES,
                ContextType.RELATED_SYMBOLS
            ])
        
        request = ContextRequest(
            project_id=project_id,
            focus_file=file_path,
            focus_class=class_name,
            max_tokens=max_tokens,
            include_types=include_types
        )
        
        return await self.context_assembler.assemble_context(request)
    
    async def get_context_for_error(
        self,
        project_id: int,
        error_message: str,
        file_path: Optional[str] = None,
        max_tokens: int = 24000
    ) -> AssembledContext:
        """Get context for debugging an error"""
        
        include_types = [
            ContextType.ERROR_CONTEXT,
            ContextType.SIMILAR_CODE
        ]
        
        if file_path:
            include_types.append(ContextType.CURRENT_FILE)
        
        request = ContextRequest(
            project_id=project_id,
            focus_file=file_path,
            error_message=error_message,
            query=error_message,  # Use error as query for semantic search
            max_tokens=max_tokens,
            include_types=include_types
        )
        
        return await self.context_assembler.assemble_context(request)
    
    async def get_context_for_query(
        self,
        project_id: int,
        query: str,
        max_tokens: int = 20000,
        file_filter: Optional[str] = None
    ) -> AssembledContext:
        """Get context for a general query"""
        
        include_types = [
            ContextType.SIMILAR_CODE,
            ContextType.RELATED_SYMBOLS
        ]
        
        request = ContextRequest(
            project_id=project_id,
            query=query,
            max_tokens=max_tokens,
            include_types=include_types
        )
        
        return await self.context_assembler.assemble_context(request)
    
    async def get_context_for_code_generation(
        self,
        project_id: int,
        target_file: str,
        description: str,
        max_tokens: int = 28000
    ) -> AssembledContext:
        """Get context for generating new code"""
        
        include_types = [
            ContextType.CURRENT_FILE,
            ContextType.DEPENDENCIES,
            ContextType.SIMILAR_CODE,
            ContextType.RELATED_SYMBOLS
        ]
        
        request = ContextRequest(
            project_id=project_id,
            focus_file=target_file,
            query=description,
            max_tokens=max_tokens,
            include_types=include_types
        )
        
        return await self.context_assembler.assemble_context(request)
    
    async def get_context_for_refactoring(
        self,
        project_id: int,
        file_path: str,
        target_symbol: Optional[str] = None,
        max_tokens: int = 32000
    ) -> AssembledContext:
        """Get context for refactoring code"""
        
        include_types = [
            ContextType.CURRENT_FILE,
            ContextType.DEPENDENCIES,
            ContextType.DEPENDENTS,
            ContextType.RELATED_SYMBOLS
        ]
        
        request = ContextRequest(
            project_id=project_id,
            focus_file=file_path,
            focus_function=target_symbol if target_symbol else None,
            max_tokens=max_tokens,
            include_types=include_types
        )
        
        return await self.context_assembler.assemble_context(request)
    
    def get_context_statistics(self, project_id: int) -> Dict[str, Any]:
        """Get context assembly statistics"""
        try:
            # Get indexing statistics
            indexing_stats = self.indexing_service.get_project_statistics(project_id)
            
            # Get vector store statistics
            vector_stats = self.indexing_service.vector_store.get_collection_stats(project_id)
            
            return {
                "project_id": project_id,
                "indexing": indexing_stats,
                "vector_store": vector_stats,
                "context_assembler": {
                    "max_context_tokens": self.context_assembler.max_context_tokens,
                    "context_overlap_tokens": self.context_assembler.context_overlap_tokens
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get context statistics: {e}")
            return {"error": str(e)}
    
    async def validate_context_quality(self, context: AssembledContext) -> Dict[str, Any]:
        """Validate the quality of assembled context"""
        try:
            quality_metrics = {
                "total_items": len(context.items),
                "total_tokens": context.total_tokens,
                "total_lines": context.total_lines,
                "truncated": context.truncated,
                "context_types": {}
            }
            
            # Analyze context types
            for item in context.items:
                context_type = item.context_type.value
                if context_type not in quality_metrics["context_types"]:
                    quality_metrics["context_types"][context_type] = {
                        "count": 0,
                        "total_tokens": 0,
                        "avg_relevance": 0.0
                    }
                
                type_stats = quality_metrics["context_types"][context_type]
                type_stats["count"] += 1
                type_stats["total_tokens"] += item.get_token_count()
                type_stats["avg_relevance"] = (
                    (type_stats["avg_relevance"] * (type_stats["count"] - 1) + item.relevance_score) 
                    / type_stats["count"]
                )
            
            # Calculate overall quality score
            if context.items:
                avg_relevance = sum(item.relevance_score for item in context.items) / len(context.items)
                token_efficiency = min(1.0, context.total_tokens / 20000)  # Optimal around 20k tokens
                type_diversity = len(quality_metrics["context_types"]) / 5  # Max 5 types
                
                quality_score = (avg_relevance * 0.5 + token_efficiency * 0.3 + type_diversity * 0.2)
                quality_metrics["quality_score"] = min(1.0, quality_score)
            else:
                quality_metrics["quality_score"] = 0.0
            
            return quality_metrics
            
        except Exception as e:
            logger.error(f"Failed to validate context quality: {e}")
            return {"error": str(e)}
    
    async def optimize_context_for_model(
        self,
        context: AssembledContext,
        model_name: str,
        task_type: str = "general"
    ) -> AssembledContext:
        """Optimize context for specific AI model and task"""
        try:
            # Model-specific optimizations
            if "gpt" in model_name.lower():
                # GPT models prefer structured context
                optimized_content = context.get_formatted_context(include_metadata=True)
            elif "claude" in model_name.lower():
                # Claude models handle longer context well
                optimized_content = context.get_formatted_context(include_metadata=False)
            else:
                # Default formatting
                optimized_content = context.get_formatted_context(include_metadata=True)
            
            # Task-specific optimizations
            if task_type == "code_generation":
                # Prioritize examples and patterns
                context.items.sort(key=lambda x: (
                    x.context_type == ContextType.SIMILAR_CODE,
                    x.relevance_score
                ), reverse=True)
            elif task_type == "debugging":
                # Prioritize error context and dependencies
                context.items.sort(key=lambda x: (
                    x.context_type == ContextType.ERROR_CONTEXT,
                    x.context_type == ContextType.DEPENDENCIES,
                    x.relevance_score
                ), reverse=True)
            
            # Update context summary
            context.context_summary += f" | Optimized for {model_name} ({task_type})"
            context.metadata["optimized_for"] = {"model": model_name, "task": task_type}
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to optimize context: {e}")
            return context

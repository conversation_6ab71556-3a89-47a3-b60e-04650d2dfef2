"""
Core tools for AI agents
"""
import os
import subprocess
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any

from src.tools.tool_registry import BaseTool, ToolDefinition, ToolParameter, ToolResult
from src.config.logging_config import get_logger
from config.settings import settings

logger = get_logger(__name__)


class ReadFileTool(BaseTool):
    """Tool for reading file contents"""
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name="read_file",
            description="Read the contents of a file",
            parameters=[
                ToolParameter(
                    name="file_path",
                    type="string",
                    description="Path to the file to read (relative to project root)"
                ),
                ToolParameter(
                    name="start_line",
                    type="integer",
                    description="Starting line number (1-based, optional)",
                    required=False
                ),
                ToolParameter(
                    name="end_line",
                    type="integer",
                    description="Ending line number (1-based, optional)",
                    required=False
                )
            ],
            category="file_operations",
            enabled=True
        )
    
    async def execute(self, arguments: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        try:
            file_path = arguments["file_path"]
            start_line = arguments.get("start_line")
            end_line = arguments.get("end_line")
            
            # Get project root from context
            project_root = context.get("project_root", ".")
            full_path = Path(project_root) / file_path
            
            # Security check: ensure file is within project
            if not self._is_safe_path(full_path, project_root):
                return ToolResult(
                    success=False,
                    error="File path is outside project directory"
                )
            
            # Check if file exists
            if not full_path.exists():
                return ToolResult(
                    success=False,
                    error=f"File not found: {file_path}"
                )
            
            # Read file
            with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                if start_line is not None or end_line is not None:
                    lines = f.readlines()
                    start_idx = (start_line - 1) if start_line else 0
                    end_idx = end_line if end_line else len(lines)
                    content = ''.join(lines[start_idx:end_idx])
                else:
                    content = f.read()
            
            return ToolResult(
                success=True,
                result={
                    "content": content,
                    "file_path": file_path,
                    "size": len(content),
                    "lines": len(content.split('\n'))
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Failed to read file: {str(e)}"
            )
    
    def _is_safe_path(self, file_path: Path, project_root: str) -> bool:
        """Check if file path is safe (within project)"""
        try:
            file_path.resolve().relative_to(Path(project_root).resolve())
            return True
        except ValueError:
            return False


class WriteFileTool(BaseTool):
    """Tool for writing file contents"""
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name="write_file",
            description="Write content to a file",
            parameters=[
                ToolParameter(
                    name="file_path",
                    type="string",
                    description="Path to the file to write (relative to project root)"
                ),
                ToolParameter(
                    name="content",
                    type="string",
                    description="Content to write to the file"
                ),
                ToolParameter(
                    name="create_backup",
                    type="boolean",
                    description="Create backup of existing file",
                    required=False,
                    default=True
                )
            ],
            category="file_operations",
            enabled=True,
            dangerous=True  # File modification is potentially dangerous
        )
    
    async def execute(self, arguments: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        try:
            file_path = arguments["file_path"]
            content = arguments["content"]
            create_backup = arguments.get("create_backup", True)
            
            # Get project root from context
            project_root = context.get("project_root", ".")
            full_path = Path(project_root) / file_path
            
            # Security check
            if not self._is_safe_path(full_path, project_root):
                return ToolResult(
                    success=False,
                    error="File path is outside project directory"
                )
            
            # Create backup if file exists
            backup_path = None
            if create_backup and full_path.exists():
                backup_path = full_path.with_suffix(full_path.suffix + '.backup')
                import shutil
                shutil.copy2(full_path, backup_path)
            
            # Create directory if it doesn't exist
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write file
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return ToolResult(
                success=True,
                result={
                    "file_path": file_path,
                    "bytes_written": len(content.encode('utf-8')),
                    "backup_created": backup_path is not None,
                    "backup_path": str(backup_path) if backup_path else None
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Failed to write file: {str(e)}"
            )
    
    def _is_safe_path(self, file_path: Path, project_root: str) -> bool:
        """Check if file path is safe (within project)"""
        try:
            file_path.resolve().relative_to(Path(project_root).resolve())
            return True
        except ValueError:
            return False


class ListFilesTool(BaseTool):
    """Tool for listing files in a directory"""
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name="list_files",
            description="List files and directories",
            parameters=[
                ToolParameter(
                    name="directory_path",
                    type="string",
                    description="Path to directory to list (relative to project root)",
                    default="."
                ),
                ToolParameter(
                    name="recursive",
                    type="boolean",
                    description="List files recursively",
                    required=False,
                    default=False
                ),
                ToolParameter(
                    name="file_pattern",
                    type="string",
                    description="File pattern to match (e.g., '*.py')",
                    required=False
                )
            ],
            category="file_operations",
            enabled=True
        )
    
    async def execute(self, arguments: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        try:
            directory_path = arguments.get("directory_path", ".")
            recursive = arguments.get("recursive", False)
            file_pattern = arguments.get("file_pattern")
            
            # Get project root from context
            project_root = context.get("project_root", ".")
            full_path = Path(project_root) / directory_path
            
            # Security check
            if not self._is_safe_path(full_path, project_root):
                return ToolResult(
                    success=False,
                    error="Directory path is outside project directory"
                )
            
            # Check if directory exists
            if not full_path.exists():
                return ToolResult(
                    success=False,
                    error=f"Directory not found: {directory_path}"
                )
            
            if not full_path.is_dir():
                return ToolResult(
                    success=False,
                    error=f"Path is not a directory: {directory_path}"
                )
            
            # List files
            files = []
            directories = []
            
            if recursive:
                pattern = file_pattern or "*"
                for item in full_path.rglob(pattern):
                    relative_path = item.relative_to(Path(project_root))
                    if item.is_file():
                        files.append({
                            "path": str(relative_path),
                            "name": item.name,
                            "size": item.stat().st_size,
                            "modified": item.stat().st_mtime
                        })
                    elif item.is_dir():
                        directories.append({
                            "path": str(relative_path),
                            "name": item.name
                        })
            else:
                for item in full_path.iterdir():
                    if file_pattern and not item.match(file_pattern):
                        continue
                    
                    relative_path = item.relative_to(Path(project_root))
                    if item.is_file():
                        files.append({
                            "path": str(relative_path),
                            "name": item.name,
                            "size": item.stat().st_size,
                            "modified": item.stat().st_mtime
                        })
                    elif item.is_dir():
                        directories.append({
                            "path": str(relative_path),
                            "name": item.name
                        })
            
            return ToolResult(
                success=True,
                result={
                    "directory": directory_path,
                    "files": files,
                    "directories": directories,
                    "total_files": len(files),
                    "total_directories": len(directories)
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Failed to list files: {str(e)}"
            )
    
    def _is_safe_path(self, file_path: Path, project_root: str) -> bool:
        """Check if file path is safe (within project)"""
        try:
            file_path.resolve().relative_to(Path(project_root).resolve())
            return True
        except ValueError:
            return False


class RunCommandTool(BaseTool):
    """Tool for running shell commands (sandboxed)"""
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name="run_command",
            description="Execute a shell command in a sandboxed environment",
            parameters=[
                ToolParameter(
                    name="command",
                    type="string",
                    description="Command to execute"
                ),
                ToolParameter(
                    name="working_directory",
                    type="string",
                    description="Working directory for command execution",
                    required=False
                ),
                ToolParameter(
                    name="timeout",
                    type="integer",
                    description="Timeout in seconds",
                    required=False,
                    default=30
                )
            ],
            category="system",
            enabled=True,
            dangerous=True,
            timeout_seconds=60
        )
    
    async def execute(self, arguments: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        try:
            command = arguments["command"]
            working_directory = arguments.get("working_directory")
            timeout = arguments.get("timeout", 30)
            
            # Get project root from context
            project_root = context.get("project_root", ".")
            
            # Set working directory
            if working_directory:
                cwd = Path(project_root) / working_directory
                if not self._is_safe_path(cwd, project_root):
                    return ToolResult(
                        success=False,
                        error="Working directory is outside project directory"
                    )
            else:
                cwd = Path(project_root)
            
            # Security: Check for dangerous commands
            if self._is_dangerous_command(command):
                return ToolResult(
                    success=False,
                    error="Command contains potentially dangerous operations"
                )
            
            # Execute command
            process = await asyncio.create_subprocess_shell(
                command,
                cwd=cwd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=self._get_safe_environment()
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return ToolResult(
                    success=False,
                    error=f"Command timed out after {timeout} seconds"
                )
            
            return ToolResult(
                success=process.returncode == 0,
                result={
                    "stdout": stdout.decode('utf-8', errors='ignore'),
                    "stderr": stderr.decode('utf-8', errors='ignore'),
                    "return_code": process.returncode,
                    "command": command,
                    "working_directory": str(cwd)
                },
                error=stderr.decode('utf-8', errors='ignore') if process.returncode != 0 else None
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Failed to execute command: {str(e)}"
            )
    
    def _is_safe_path(self, file_path: Path, project_root: str) -> bool:
        """Check if file path is safe (within project)"""
        try:
            file_path.resolve().relative_to(Path(project_root).resolve())
            return True
        except ValueError:
            return False
    
    def _is_dangerous_command(self, command: str) -> bool:
        """Check if command contains dangerous operations"""
        dangerous_patterns = [
            r'\brm\s+-rf\b',  # rm -rf
            r'\bsudo\b',      # sudo
            r'\bsu\b',        # su
            r'\bchmod\s+777\b',  # chmod 777
            r'\b>\s*/dev/',   # redirect to /dev/
            r'\bcurl.*\|\s*sh\b',  # curl | sh
            r'\bwget.*\|\s*sh\b',  # wget | sh
            r'\bmkfs\b',      # mkfs
            r'\bdd\s+if=',    # dd if=
            r'\bformat\b',    # format (Windows)
            r'\bdel\s+/[sq]\b',  # del /s /q (Windows)
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                return True
        
        return False
    
    def _get_safe_environment(self) -> Dict[str, str]:
        """Get safe environment variables"""
        safe_env = {
            "PATH": os.environ.get("PATH", ""),
            "HOME": os.environ.get("HOME", ""),
            "USER": os.environ.get("USER", ""),
            "LANG": os.environ.get("LANG", "en_US.UTF-8"),
            "LC_ALL": os.environ.get("LC_ALL", "en_US.UTF-8"),
        }
        
        # Remove potentially dangerous environment variables
        return {k: v for k, v in safe_env.items() if v}


class SearchCodebaseTool(BaseTool):
    """Tool for searching the codebase"""

    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name="search_codebase",
            description="Search for code patterns, functions, or text in the codebase",
            parameters=[
                ToolParameter(
                    name="query",
                    type="string",
                    description="Search query (text or pattern to find)"
                ),
                ToolParameter(
                    name="search_type",
                    type="string",
                    description="Type of search to perform",
                    enum=["text", "semantic", "symbol", "regex"],
                    default="text"
                ),
                ToolParameter(
                    name="file_pattern",
                    type="string",
                    description="File pattern to limit search (e.g., '*.py')",
                    required=False
                ),
                ToolParameter(
                    name="max_results",
                    type="integer",
                    description="Maximum number of results to return",
                    required=False,
                    default=20
                )
            ],
            category="search",
            enabled=True
        )

    async def execute(self, arguments: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        try:
            query = arguments["query"]
            search_type = arguments.get("search_type", "text")
            file_pattern = arguments.get("file_pattern")
            max_results = arguments.get("max_results", 20)

            # Get services from context
            indexing_service = context.get("indexing_service")
            project_id = context.get("project_id")

            if not indexing_service or not project_id:
                return ToolResult(
                    success=False,
                    error="Indexing service or project ID not available in context"
                )

            results = []

            if search_type == "semantic":
                # Use semantic search
                semantic_results = await indexing_service.search_code_semantic(
                    project_id, query, max_results
                )

                for result in semantic_results:
                    if file_pattern and not Path(result["file_path"]).match(file_pattern):
                        continue

                    results.append({
                        "file_path": result["file_path"],
                        "start_line": result.get("start_line", 1),
                        "end_line": result.get("end_line", 1),
                        "content": result["text"],
                        "score": result.get("similarity_score", 0.0),
                        "type": "semantic"
                    })

            elif search_type == "symbol":
                # Search for symbols
                symbol_results = indexing_service.search_symbols(project_id, query)

                for symbol in symbol_results:
                    if file_pattern and not Path(symbol["file_path"]).match(file_pattern):
                        continue

                    results.append({
                        "file_path": symbol["file_path"],
                        "start_line": symbol.get("start_line", 1),
                        "end_line": symbol.get("end_line", 1),
                        "content": f"{symbol.get('type', 'unknown')} {symbol.get('name', 'unknown')}",
                        "symbol_name": symbol.get("name"),
                        "symbol_type": symbol.get("type"),
                        "type": "symbol"
                    })

            else:
                # Text or regex search - implement basic file search
                project_root = context.get("project_root", ".")
                results = await self._search_files(
                    project_root, query, search_type, file_pattern, max_results
                )

            return ToolResult(
                success=True,
                result={
                    "query": query,
                    "search_type": search_type,
                    "results": results[:max_results],
                    "total_found": len(results)
                }
            )

        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Search failed: {str(e)}"
            )

    async def _search_files(
        self,
        project_root: str,
        query: str,
        search_type: str,
        file_pattern: Optional[str],
        max_results: int
    ) -> List[Dict[str, Any]]:
        """Search files using text or regex"""
        results = []
        project_path = Path(project_root)

        # Get all files to search
        if file_pattern:
            files = list(project_path.rglob(file_pattern))
        else:
            # Default to common code file extensions
            extensions = ["*.py", "*.js", "*.ts", "*.java", "*.cpp", "*.c", "*.rs", "*.go"]
            files = []
            for ext in extensions:
                files.extend(project_path.rglob(ext))

        # Search in files
        for file_path in files:
            if len(results) >= max_results:
                break

            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()

                for line_num, line in enumerate(lines, 1):
                    if search_type == "regex":
                        import re
                        if re.search(query, line, re.IGNORECASE):
                            results.append({
                                "file_path": str(file_path.relative_to(project_path)),
                                "line_number": line_num,
                                "content": line.strip(),
                                "type": "regex_match"
                            })
                    else:  # text search
                        if query.lower() in line.lower():
                            results.append({
                                "file_path": str(file_path.relative_to(project_path)),
                                "line_number": line_num,
                                "content": line.strip(),
                                "type": "text_match"
                            })

                    if len(results) >= max_results:
                        break

            except Exception as e:
                logger.warning(f"Failed to search file {file_path}: {e}")
                continue

        return results


class AnalyzeErrorTool(BaseTool):
    """Tool for analyzing errors and providing solutions"""

    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name="analyze_error",
            description="Analyze an error message and provide potential solutions",
            parameters=[
                ToolParameter(
                    name="error_message",
                    type="string",
                    description="The error message to analyze"
                ),
                ToolParameter(
                    name="error_type",
                    type="string",
                    description="Type of error",
                    enum=["syntax", "runtime", "compilation", "test", "unknown"],
                    required=False,
                    default="unknown"
                ),
                ToolParameter(
                    name="file_path",
                    type="string",
                    description="File where the error occurred",
                    required=False
                ),
                ToolParameter(
                    name="line_number",
                    type="integer",
                    description="Line number where the error occurred",
                    required=False
                ),
                ToolParameter(
                    name="stack_trace",
                    type="string",
                    description="Full stack trace if available",
                    required=False
                )
            ],
            category="analysis",
            enabled=True
        )

    async def execute(self, arguments: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        try:
            error_message = arguments["error_message"]
            error_type = arguments.get("error_type", "unknown")
            file_path = arguments.get("file_path")
            line_number = arguments.get("line_number")
            stack_trace = arguments.get("stack_trace")

            # Analyze the error
            analysis = self._analyze_error_message(error_message, error_type)

            # Get context if file information is available
            context_info = None
            if file_path and context.get("indexing_service") and context.get("project_id"):
                try:
                    # Get file context around the error
                    indexing_service = context["indexing_service"]
                    project_id = context["project_id"]

                    # Search for similar errors or related code
                    similar_results = await indexing_service.search_code_semantic(
                        project_id, error_message, max_results=3
                    )

                    context_info = {
                        "similar_code": similar_results,
                        "file_path": file_path,
                        "line_number": line_number
                    }
                except Exception as e:
                    logger.warning(f"Failed to get error context: {e}")

            return ToolResult(
                success=True,
                result={
                    "error_message": error_message,
                    "error_type": error_type,
                    "analysis": analysis,
                    "suggestions": self._get_error_suggestions(error_message, error_type),
                    "context": context_info,
                    "severity": self._assess_error_severity(error_message, error_type)
                }
            )

        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error analysis failed: {str(e)}"
            )

    def _analyze_error_message(self, error_message: str, error_type: str) -> str:
        """Analyze error message and provide explanation"""
        error_lower = error_message.lower()

        # Common error patterns
        if "syntax error" in error_lower or "syntaxerror" in error_lower:
            return "This is a syntax error, meaning the code structure is invalid and doesn't follow the language's grammar rules."

        elif "name" in error_lower and "not defined" in error_lower:
            return "This is a NameError, indicating that a variable, function, or module name is being used before it's defined or imported."

        elif "module" in error_lower and "not found" in error_lower:
            return "This is a ModuleNotFoundError, meaning Python cannot find the specified module. It may not be installed or not in the Python path."

        elif "attribute" in error_lower and "has no attribute" in error_lower:
            return "This is an AttributeError, indicating that an object doesn't have the specified attribute or method."

        elif "index" in error_lower and "out of range" in error_lower:
            return "This is an IndexError, meaning you're trying to access a list, tuple, or string index that doesn't exist."

        elif "key" in error_lower and "keyerror" in error_lower:
            return "This is a KeyError, indicating that a dictionary key doesn't exist."

        elif "type" in error_lower and "object" in error_lower:
            return "This is a TypeError, meaning an operation is being performed on an inappropriate type."

        elif "indentation" in error_lower:
            return "This is an IndentationError, meaning the code indentation is incorrect."

        elif "import" in error_lower and "error" in error_lower:
            return "This is an ImportError, indicating problems with importing a module or specific items from a module."

        else:
            return f"This appears to be a {error_type} error. The error message suggests there's an issue with the code execution or structure."

    def _get_error_suggestions(self, error_message: str, error_type: str) -> List[str]:
        """Get suggestions for fixing the error"""
        suggestions = []
        error_lower = error_message.lower()

        if "syntax error" in error_lower:
            suggestions.extend([
                "Check for missing parentheses, brackets, or quotes",
                "Verify proper indentation",
                "Look for typos in keywords",
                "Ensure all opened brackets/parentheses are closed"
            ])

        elif "name" in error_lower and "not defined" in error_lower:
            suggestions.extend([
                "Check if the variable is defined before use",
                "Verify the variable name spelling",
                "Ensure proper import statements",
                "Check variable scope (local vs global)"
            ])

        elif "module" in error_lower and "not found" in error_lower:
            suggestions.extend([
                "Install the missing module using pip",
                "Check the module name spelling",
                "Verify the module is in the Python path",
                "Check if it's a relative import issue"
            ])

        elif "attribute" in error_lower:
            suggestions.extend([
                "Check the object type and available methods",
                "Verify the attribute name spelling",
                "Ensure the object is properly initialized",
                "Check the documentation for correct usage"
            ])

        elif "index" in error_lower and "out of range" in error_lower:
            suggestions.extend([
                "Check the list/array length before accessing",
                "Use len() to verify bounds",
                "Consider using try/except for safe access",
                "Check if the index calculation is correct"
            ])

        else:
            suggestions.extend([
                "Read the error message carefully for clues",
                "Check the line number mentioned in the error",
                "Look for recent changes that might have caused the issue",
                "Consider adding debug prints to understand the flow"
            ])

        return suggestions

    def _assess_error_severity(self, error_message: str, error_type: str) -> str:
        """Assess the severity of the error"""
        error_lower = error_message.lower()

        # Critical errors that prevent execution
        if any(keyword in error_lower for keyword in ["syntax error", "indentation", "import"]):
            return "critical"

        # High severity runtime errors
        elif any(keyword in error_lower for keyword in ["segmentation fault", "memory", "stack overflow"]):
            return "high"

        # Medium severity errors
        elif any(keyword in error_lower for keyword in ["attribute", "type", "value"]):
            return "medium"

        # Low severity errors
        else:
            return "low"

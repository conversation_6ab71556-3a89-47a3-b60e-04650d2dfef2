"""
File database model
"""
from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, BigInteger
from sqlalchemy.orm import relationship
from pydantic import BaseModel

from src.config.database import Base


class File(Base):
    """File database model"""
    __tablename__ = "files"
    
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, index=True)
    path = Column(String(1000), nullable=False, index=True)
    name = Column(String(255), nullable=False, index=True)
    extension = Column(String(50), nullable=False, index=True)
    size = Column(BigInteger, nullable=False)
    content_hash = Column(String(64), nullable=False, index=True)  # SHA-256 hash
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    project = relationship("Project", back_populates="files")


class FileCreate(BaseModel):
    """File creation schema"""
    project_id: int
    path: str
    name: str
    extension: str
    size: int
    content_hash: str


class FileUpdate(BaseModel):
    """File update schema"""
    size: Optional[int] = None
    content_hash: Optional[str] = None


class FileResponse(BaseModel):
    """File response schema"""
    id: int
    project_id: int
    path: str
    name: str
    extension: str
    size: int
    content_hash: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

version: '3.8'

services:
  # Main EchoCode application (development mode)
  echocode-dev:
    build: 
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    environment:
      - DEBUG=true
      - REDIS_URL=redis://redis:6379
      - OLLAMA_URL=http://ollama:11434
      - DATABASE_URL=sqlite:///app/data/echocode.db
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-test_key}
      - LOG_LEVEL=DEBUG
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./projects:/app/projects
      - .:/app  # Mount source code for development
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - echocode-network

  # Redis for task queue and caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - echocode-network

  # Optional: Ollama for local embeddings (can be disabled for basic testing)
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_ORIGINS=*
    restart: unless-stopped
    networks:
      - echocode-network
    profiles:
      - full  # Only start with --profile full

volumes:
  redis_data:
  ollama_data:

networks:
  echocode-network:
    driver: bridge

"""
Context Assembly System for intelligent code context selection
"""
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import re
from pathlib import Path

from src.indexing.indexing_service import IndexingService
from src.config.logging_config import get_logger
from config.settings import settings

logger = get_logger(__name__)


class ContextType(Enum):
    """Types of context that can be included"""
    CURRENT_FILE = "current_file"
    DEPENDENCIES = "dependencies"
    DEPENDENTS = "dependents"
    RELATED_SYMBOLS = "related_symbols"
    SIMILAR_CODE = "similar_code"
    ERROR_CONTEXT = "error_context"
    DOCUMENTATION = "documentation"


@dataclass
class ContextItem:
    """Represents a piece of context"""
    content: str
    file_path: str
    start_line: int
    end_line: int
    context_type: ContextType
    relevance_score: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_token_count(self) -> int:
        """Estimate token count (rough approximation)"""
        # Rough estimate: 1 token ≈ 4 characters for code
        return len(self.content) // 4
    
    def get_line_count(self) -> int:
        """Get number of lines in this context"""
        return self.end_line - self.start_line + 1


@dataclass
class ContextRequest:
    """Request for context assembly"""
    project_id: int
    focus_file: Optional[str] = None
    focus_function: Optional[str] = None
    focus_class: Optional[str] = None
    query: Optional[str] = None
    error_message: Optional[str] = None
    max_tokens: int = 32000
    include_types: List[ContextType] = field(default_factory=lambda: [
        ContextType.CURRENT_FILE,
        ContextType.DEPENDENCIES,
        ContextType.RELATED_SYMBOLS
    ])
    exclude_patterns: List[str] = field(default_factory=list)
    priority_files: List[str] = field(default_factory=list)


@dataclass
class AssembledContext:
    """Result of context assembly"""
    items: List[ContextItem]
    total_tokens: int
    total_lines: int
    context_summary: str
    truncated: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_formatted_context(self, include_metadata: bool = True) -> str:
        """Get formatted context string"""
        sections = []
        
        if include_metadata:
            sections.append(f"# Context Summary\n{self.context_summary}\n")
            sections.append(f"# Total: {len(self.items)} files, {self.total_lines} lines, ~{self.total_tokens} tokens\n")
        
        # Group by context type
        grouped = {}
        for item in self.items:
            if item.context_type not in grouped:
                grouped[item.context_type] = []
            grouped[item.context_type].append(item)
        
        # Format each group
        for context_type, items in grouped.items():
            sections.append(f"\n## {context_type.value.replace('_', ' ').title()}\n")
            
            for item in items:
                sections.append(f"\n### {item.file_path} (lines {item.start_line}-{item.end_line})")
                if include_metadata and item.metadata:
                    sections.append(f"<!-- Relevance: {item.relevance_score:.2f}, {item.metadata} -->")
                sections.append(f"```\n{item.content}\n```\n")
        
        return "\n".join(sections)


class ContextAssembler:
    """Assembles intelligent context for AI models"""
    
    def __init__(self, indexing_service: IndexingService):
        self.indexing_service = indexing_service
        self.max_context_tokens = settings.max_context_tokens
        self.context_overlap_tokens = settings.context_overlap_tokens
    
    async def assemble_context(self, request: ContextRequest) -> AssembledContext:
        """Assemble context based on request"""
        try:
            context_items = []
            
            # Collect context items by type
            for context_type in request.include_types:
                items = await self._collect_context_by_type(context_type, request)
                context_items.extend(items)
            
            # Score and rank context items
            scored_items = self._score_context_items(context_items, request)
            
            # Select best items within token limit
            selected_items = self._select_within_token_limit(scored_items, request.max_tokens)
            
            # Generate summary
            summary = self._generate_context_summary(selected_items, request)
            
            # Calculate totals
            total_tokens = sum(item.get_token_count() for item in selected_items)
            total_lines = sum(item.get_line_count() for item in selected_items)
            
            result = AssembledContext(
                items=selected_items,
                total_tokens=total_tokens,
                total_lines=total_lines,
                context_summary=summary,
                truncated=len(selected_items) < len(scored_items),
                metadata={
                    "original_items": len(context_items),
                    "scored_items": len(scored_items),
                    "selected_items": len(selected_items),
                    "request": request
                }
            )
            
            logger.info(f"Assembled context: {len(selected_items)} items, {total_tokens} tokens")
            return result
            
        except Exception as e:
            logger.error(f"Failed to assemble context: {e}")
            return AssembledContext(
                items=[],
                total_tokens=0,
                total_lines=0,
                context_summary="Failed to assemble context",
                metadata={"error": str(e)}
            )
    
    async def _collect_context_by_type(self, context_type: ContextType, request: ContextRequest) -> List[ContextItem]:
        """Collect context items of a specific type"""
        items = []
        
        try:
            if context_type == ContextType.CURRENT_FILE:
                items.extend(await self._get_current_file_context(request))
            
            elif context_type == ContextType.DEPENDENCIES:
                items.extend(await self._get_dependency_context(request))
            
            elif context_type == ContextType.DEPENDENTS:
                items.extend(await self._get_dependent_context(request))
            
            elif context_type == ContextType.RELATED_SYMBOLS:
                items.extend(await self._get_related_symbols_context(request))
            
            elif context_type == ContextType.SIMILAR_CODE:
                items.extend(await self._get_similar_code_context(request))
            
            elif context_type == ContextType.ERROR_CONTEXT:
                items.extend(await self._get_error_context(request))
            
            elif context_type == ContextType.DOCUMENTATION:
                items.extend(await self._get_documentation_context(request))
            
        except Exception as e:
            logger.error(f"Failed to collect {context_type.value} context: {e}")
        
        return items
    
    async def _get_current_file_context(self, request: ContextRequest) -> List[ContextItem]:
        """Get context from the current file"""
        items = []
        
        if not request.focus_file:
            return items
        
        try:
            # Read file content
            file_content = await self._read_file_content(request.project_id, request.focus_file)
            if not file_content:
                return items
            
            # If focusing on specific function/class, extract that
            if request.focus_function or request.focus_class:
                symbols = self.indexing_service.get_file_symbols(request.project_id, request.focus_file)
                
                for symbol in symbols:
                    if (request.focus_function and symbol.get("name") == request.focus_function) or \
                       (request.focus_class and symbol.get("name") == request.focus_class):
                        
                        lines = file_content.split('\n')
                        start_line = max(0, symbol.get("start_line", 1) - 1)
                        end_line = min(len(lines), symbol.get("end_line", len(lines)))
                        
                        symbol_content = '\n'.join(lines[start_line:end_line])
                        
                        items.append(ContextItem(
                            content=symbol_content,
                            file_path=request.focus_file,
                            start_line=start_line + 1,
                            end_line=end_line,
                            context_type=ContextType.CURRENT_FILE,
                            relevance_score=1.0,
                            metadata={"symbol": symbol}
                        ))
            else:
                # Include entire file (may be chunked later)
                lines = file_content.split('\n')
                items.append(ContextItem(
                    content=file_content,
                    file_path=request.focus_file,
                    start_line=1,
                    end_line=len(lines),
                    context_type=ContextType.CURRENT_FILE,
                    relevance_score=1.0,
                    metadata={"full_file": True}
                ))
        
        except Exception as e:
            logger.error(f"Failed to get current file context: {e}")
        
        return items
    
    async def _get_dependency_context(self, request: ContextRequest) -> List[ContextItem]:
        """Get context from file dependencies"""
        items = []
        
        if not request.focus_file:
            return items
        
        try:
            dependencies = self.indexing_service.get_file_dependencies(request.project_id, request.focus_file)
            
            for dep_file in dependencies[:5]:  # Limit to top 5 dependencies
                content = await self._read_file_content(request.project_id, dep_file)
                if content:
                    # Get key symbols from dependency
                    symbols = self.indexing_service.get_file_symbols(request.project_id, dep_file)
                    
                    # Include important symbols (classes, main functions)
                    for symbol in symbols[:3]:  # Top 3 symbols
                        if symbol.get("type") in ["class", "function"]:
                            symbol_content = self._extract_symbol_content(content, symbol)
                            if symbol_content:
                                items.append(ContextItem(
                                    content=symbol_content,
                                    file_path=dep_file,
                                    start_line=symbol.get("start_line", 1),
                                    end_line=symbol.get("end_line", 1),
                                    context_type=ContextType.DEPENDENCIES,
                                    relevance_score=0.8,
                                    metadata={"symbol": symbol, "dependency": True}
                                ))
        
        except Exception as e:
            logger.error(f"Failed to get dependency context: {e}")
        
        return items
    
    async def _get_dependent_context(self, request: ContextRequest) -> List[ContextItem]:
        """Get context from files that depend on the current file"""
        items = []
        
        if not request.focus_file:
            return items
        
        try:
            dependents = self.indexing_service.get_file_dependents(request.project_id, request.focus_file)
            
            for dep_file in dependents[:3]:  # Limit to top 3 dependents
                content = await self._read_file_content(request.project_id, dep_file)
                if content:
                    # Include relevant usage examples
                    usage_lines = self._find_usage_lines(content, request.focus_file)
                    
                    for start_line, end_line, usage_content in usage_lines[:2]:
                        items.append(ContextItem(
                            content=usage_content,
                            file_path=dep_file,
                            start_line=start_line,
                            end_line=end_line,
                            context_type=ContextType.DEPENDENTS,
                            relevance_score=0.7,
                            metadata={"usage_example": True}
                        ))
        
        except Exception as e:
            logger.error(f"Failed to get dependent context: {e}")
        
        return items
    
    async def _get_related_symbols_context(self, request: ContextRequest) -> List[ContextItem]:
        """Get context from related symbols"""
        items = []
        
        if not (request.focus_function or request.focus_class):
            return items
        
        try:
            symbol_name = request.focus_function or request.focus_class
            related_symbols = self.indexing_service.search_symbols(request.project_id, symbol_name)
            
            for symbol in related_symbols[:5]:
                if symbol.get("file_path") != request.focus_file:  # Exclude current file
                    content = await self._read_file_content(request.project_id, symbol["file_path"])
                    if content:
                        symbol_content = self._extract_symbol_content(content, symbol)
                        if symbol_content:
                            items.append(ContextItem(
                                content=symbol_content,
                                file_path=symbol["file_path"],
                                start_line=symbol.get("start_line", 1),
                                end_line=symbol.get("end_line", 1),
                                context_type=ContextType.RELATED_SYMBOLS,
                                relevance_score=0.6,
                                metadata={"symbol": symbol, "related": True}
                            ))
        
        except Exception as e:
            logger.error(f"Failed to get related symbols context: {e}")
        
        return items
    
    async def _get_similar_code_context(self, request: ContextRequest) -> List[ContextItem]:
        """Get context from semantically similar code"""
        items = []
        
        if not request.query:
            return items
        
        try:
            # Use semantic search to find similar code
            similar_results = await self.indexing_service.search_code_semantic(
                request.project_id, request.query, max_results=5
            )
            
            for result in similar_results:
                if result.get("file_path") != request.focus_file:  # Exclude current file
                    items.append(ContextItem(
                        content=result["text"],
                        file_path=result["file_path"],
                        start_line=result.get("start_line", 1),
                        end_line=result.get("end_line", 1),
                        context_type=ContextType.SIMILAR_CODE,
                        relevance_score=result.get("similarity_score", 0.5),
                        metadata={"semantic_search": True, "query": request.query}
                    ))
        
        except Exception as e:
            logger.error(f"Failed to get similar code context: {e}")
        
        return items
    
    async def _get_error_context(self, request: ContextRequest) -> List[ContextItem]:
        """Get context related to error messages"""
        items = []
        
        if not request.error_message:
            return items
        
        try:
            # Search for code related to the error
            error_results = await self.indexing_service.search_code_semantic(
                request.project_id, request.error_message, max_results=3
            )
            
            for result in error_results:
                items.append(ContextItem(
                    content=result["text"],
                    file_path=result["file_path"],
                    start_line=result.get("start_line", 1),
                    end_line=result.get("end_line", 1),
                    context_type=ContextType.ERROR_CONTEXT,
                    relevance_score=result.get("similarity_score", 0.5),
                    metadata={"error_related": True, "error": request.error_message}
                ))
        
        except Exception as e:
            logger.error(f"Failed to get error context: {e}")
        
        return items
    
    async def _get_documentation_context(self, request: ContextRequest) -> List[ContextItem]:
        """Get documentation context"""
        items = []

        # TODO: Implement documentation extraction
        # This would look for README files, docstrings, comments, etc.

        return items

    async def _read_file_content(self, project_id: int, file_path: str) -> Optional[str]:
        """Read file content safely"""
        try:
            # This would integrate with the file service
            # For now, return None to avoid errors
            return None
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            return None

    def _extract_symbol_content(self, file_content: str, symbol: Dict[str, Any]) -> Optional[str]:
        """Extract content for a specific symbol"""
        try:
            lines = file_content.split('\n')
            start_line = max(0, symbol.get("start_line", 1) - 1)
            end_line = min(len(lines), symbol.get("end_line", len(lines)))

            return '\n'.join(lines[start_line:end_line])
        except Exception as e:
            logger.error(f"Failed to extract symbol content: {e}")
            return None

    def _find_usage_lines(self, content: str, target_file: str) -> List[Tuple[int, int, str]]:
        """Find lines that use/import the target file"""
        usage_lines = []
        lines = content.split('\n')

        # Simple heuristic: look for import statements or file references
        target_name = Path(target_file).stem

        for i, line in enumerate(lines):
            if target_name in line and ('import' in line or 'from' in line):
                # Include some context around the usage
                start = max(0, i - 2)
                end = min(len(lines), i + 3)
                context = '\n'.join(lines[start:end])
                usage_lines.append((start + 1, end, context))

        return usage_lines

    def _score_context_items(self, items: List[ContextItem], request: ContextRequest) -> List[ContextItem]:
        """Score and rank context items by relevance"""
        scored_items = []

        for item in items:
            score = item.relevance_score

            # Boost score for priority files
            if item.file_path in request.priority_files:
                score *= 1.5

            # Boost score for current file
            if item.context_type == ContextType.CURRENT_FILE:
                score *= 1.3

            # Boost score for focused symbols
            if request.focus_function or request.focus_class:
                symbol_name = item.metadata.get("symbol", {}).get("name", "")
                if symbol_name in [request.focus_function, request.focus_class]:
                    score *= 1.4

            # Apply query-based scoring if available
            if request.query:
                query_score = self._calculate_query_relevance(item.content, request.query)
                score = (score + query_score) / 2

            # Penalize very large items
            token_count = item.get_token_count()
            if token_count > 2000:
                score *= 0.8

            # Update item score
            item.relevance_score = score
            scored_items.append(item)

        # Sort by relevance score
        scored_items.sort(key=lambda x: x.relevance_score, reverse=True)

        return scored_items

    def _calculate_query_relevance(self, content: str, query: str) -> float:
        """Calculate relevance of content to query"""
        if not query or not content:
            return 0.0

        # Simple keyword matching
        query_words = set(query.lower().split())
        content_words = set(content.lower().split())

        if not query_words:
            return 0.0

        matches = query_words.intersection(content_words)
        return len(matches) / len(query_words)

    def _select_within_token_limit(self, items: List[ContextItem], max_tokens: int) -> List[ContextItem]:
        """Select items that fit within token limit"""
        selected = []
        total_tokens = 0

        for item in items:
            item_tokens = item.get_token_count()

            if total_tokens + item_tokens <= max_tokens:
                selected.append(item)
                total_tokens += item_tokens
            else:
                # Try to include partial content if it's a large item
                if item_tokens > max_tokens // 4:  # If item is more than 25% of limit
                    remaining_tokens = max_tokens - total_tokens
                    if remaining_tokens > 100:  # Only if we have meaningful space left
                        # Truncate the content
                        truncated_content = self._truncate_content(item.content, remaining_tokens)
                        if truncated_content:
                            truncated_item = ContextItem(
                                content=truncated_content,
                                file_path=item.file_path,
                                start_line=item.start_line,
                                end_line=item.end_line,
                                context_type=item.context_type,
                                relevance_score=item.relevance_score * 0.8,  # Penalize truncation
                                metadata={**item.metadata, "truncated": True}
                            )
                            selected.append(truncated_item)
                            break
                else:
                    break

        return selected

    def _truncate_content(self, content: str, max_tokens: int) -> str:
        """Truncate content to fit within token limit"""
        # Rough estimate: 4 characters per token
        max_chars = max_tokens * 4

        if len(content) <= max_chars:
            return content

        # Try to truncate at line boundaries
        lines = content.split('\n')
        truncated_lines = []
        char_count = 0

        for line in lines:
            if char_count + len(line) + 1 <= max_chars:  # +1 for newline
                truncated_lines.append(line)
                char_count += len(line) + 1
            else:
                break

        if truncated_lines:
            return '\n'.join(truncated_lines) + '\n... [truncated]'
        else:
            # Fallback: character-based truncation
            return content[:max_chars] + '... [truncated]'

    def _generate_context_summary(self, items: List[ContextItem], request: ContextRequest) -> str:
        """Generate a summary of the assembled context"""
        if not items:
            return "No context available"

        # Count by type
        type_counts = {}
        for item in items:
            type_name = item.context_type.value
            type_counts[type_name] = type_counts.get(type_name, 0) + 1

        # Generate summary
        summary_parts = []

        if request.focus_file:
            summary_parts.append(f"Focus: {request.focus_file}")

        if request.focus_function:
            summary_parts.append(f"Function: {request.focus_function}")

        if request.focus_class:
            summary_parts.append(f"Class: {request.focus_class}")

        if request.query:
            summary_parts.append(f"Query: {request.query}")

        # Add type breakdown
        type_summary = ", ".join([f"{count} {type_name}" for type_name, count in type_counts.items()])
        summary_parts.append(f"Context: {type_summary}")

        return " | ".join(summary_parts)

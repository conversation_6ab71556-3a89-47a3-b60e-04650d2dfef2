import axios from 'axios';

const API_BASE_URL = process.env.NODE_ENV === 'production' ? '' : 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export interface Project {
  id: number;
  name: string;
  path: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  file_count: number;
}

export interface ProjectCreate {
  name: string;
  path: string;
  description?: string;
}

export interface File {
  id: number;
  project_id: number;
  path: string;
  name: string;
  extension: string;
  size: number;
  content_hash: string;
  created_at: string;
  updated_at: string;
}

export interface AgentRun {
  id: number;
  project_id: number;
  agent_type: string;
  status: string;
  config: Record<string, any>;
  started_at: string;
  completed_at?: string;
  error_message?: string;
  iterations: number;
}

export interface Tool {
  name: string;
  description: string;
  parameters: Record<string, any>;
  category: string;
  enabled: boolean;
}

export interface AppStatus {
  app_name: string;
  version: string;
  debug: boolean;
  supported_extensions: string[];
  max_context_tokens: number;
  models: {
    coding: string;
    vision: string;
    embedding: string;
  };
}

export const apiClient = {
  // System
  async getStatus(): Promise<AppStatus> {
    const response = await api.get('/api/status');
    return response.data;
  },

  async getHealth(): Promise<{ status: string }> {
    const response = await api.get('/health');
    return response.data;
  },

  // Projects
  async getProjects(): Promise<Project[]> {
    const response = await api.get('/api/projects/');
    return response.data;
  },

  async getProject(id: number): Promise<Project> {
    const response = await api.get(`/api/projects/${id}`);
    return response.data;
  },

  async createProject(project: ProjectCreate): Promise<Project> {
    const response = await api.post('/api/projects/', project);
    return response.data;
  },

  async updateProject(id: number, project: Partial<ProjectCreate>): Promise<Project> {
    const response = await api.put(`/api/projects/${id}`, project);
    return response.data;
  },

  async deleteProject(id: number): Promise<void> {
    await api.delete(`/api/projects/${id}`);
  },

  async scanProject(id: number): Promise<{ files_found: number; files_added: number; files_updated: number }> {
    const response = await api.post(`/api/projects/${id}/scan`);
    return response.data;
  },

  // Files
  async getProjectFiles(projectId: number, extension?: string): Promise<File[]> {
    const params = extension ? { extension } : {};
    const response = await api.get(`/api/files/project/${projectId}`, { params });
    return response.data;
  },

  async getFile(id: number): Promise<File> {
    const response = await api.get(`/api/files/${id}`);
    return response.data;
  },

  async getFileContent(id: number): Promise<{ content: string; encoding: string }> {
    const response = await api.get(`/api/files/${id}/content`);
    return response.data;
  },

  async updateFileContent(id: number, content: string): Promise<void> {
    await api.put(`/api/files/${id}/content`, { content });
  },

  async deleteFile(id: number): Promise<void> {
    await api.delete(`/api/files/${id}`);
  },

  async searchFiles(query: string, projectId?: number, fileTypes?: string[]): Promise<{ results: any[]; query: string }> {
    const response = await api.post('/api/files/search', {
      query,
      project_id: projectId,
      file_types: fileTypes,
    });
    return response.data;
  },

  // Agents
  async getAgentRuns(projectId?: number, status?: string): Promise<AgentRun[]> {
    const params: any = {};
    if (projectId) params.project_id = projectId;
    if (status) params.status = status;
    
    const response = await api.get('/api/agents/runs', { params });
    return response.data;
  },

  async getAgentRun(id: number): Promise<AgentRun> {
    const response = await api.get(`/api/agents/runs/${id}`);
    return response.data;
  },

  async startAgent(projectId: number, agentType: string = 'continuous_coder', config: Record<string, any> = {}): Promise<AgentRun> {
    const response = await api.post('/api/agents/start', {
      project_id: projectId,
      agent_type: agentType,
      config,
    });
    return response.data;
  },

  async controlAgent(runId: number, action: 'pause' | 'resume' | 'stop'): Promise<{ message: string }> {
    const response = await api.post(`/api/agents/runs/${runId}/control`, { action });
    return response.data;
  },

  async getAgentLogs(runId: number): Promise<{ logs: any[] }> {
    const response = await api.get(`/api/agents/runs/${runId}/logs`);
    return response.data;
  },

  async getAgentStatus(): Promise<{
    total_runs: number;
    active_runs: number;
    completed_runs: number;
    failed_runs: number;
    system_healthy: boolean;
  }> {
    const response = await api.get('/api/agents/status');
    return response.data;
  },

  // Tools
  async getTools(): Promise<Tool[]> {
    const response = await api.get('/api/tools/');
    return response.data;
  },

  async getTool(name: string): Promise<Tool> {
    const response = await api.get(`/api/tools/${name}`);
    return response.data;
  },

  async executeTool(toolName: string, args: Record<string, any>, projectId?: number): Promise<{
    success: boolean;
    result: any;
    error?: string;
    execution_time: number;
  }> {
    const response = await api.post('/api/tools/execute', {
      tool_name: toolName,
      arguments: args,
      project_id: projectId,
    });
    return response.data;
  },

  async validateToolCall(toolName: string, args: Record<string, any>): Promise<{
    valid: boolean;
    errors: string[];
  }> {
    const response = await api.post('/api/tools/validate', {
      tool_name: toolName,
      arguments: args,
    });
    return response.data;
  },

  async getToolCategories(): Promise<{ categories: string[] }> {
    const response = await api.get('/api/tools/categories/');
    return response.data;
  },
};

export default api;

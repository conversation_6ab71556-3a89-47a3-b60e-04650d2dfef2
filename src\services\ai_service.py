"""
AI service that combines OpenRouter client with context assembly
"""
import uuid
from typing import Dict, List, Optional, Any, AsyncGenerator
from sqlalchemy.orm import Session

from src.services.openrouter_client import OpenRouterClient, ModelType, Conversation
from src.services.context_service import ContextService
from src.indexing.context_assembly import ContextR<PERSON><PERSON>, ContextType
from src.config.logging_config import get_logger

logger = get_logger(__name__)


class AIService:
    """High-level AI service for code-related tasks"""
    
    def __init__(self, db: Session):
        self.db = db
        self.openrouter_client = OpenRouterClient()
        self.context_service = ContextService(db)
    
    async def __aenter__(self):
        await self.openrouter_client.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.openrouter_client.__aexit__(exc_type, exc_val, exc_tb)
    
    async def analyze_code(
        self,
        project_id: int,
        file_path: str,
        analysis_type: str = "general",
        specific_function: Optional[str] = None,
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """Analyze code with AI"""
        try:
            # Get context for the file
            if specific_function:
                context = await self.context_service.get_context_for_function(
                    project_id, file_path, specific_function
                )
            else:
                context = await self.context_service.get_context_for_file(
                    project_id, file_path
                )
            
            # Prepare prompt based on analysis type
            if analysis_type == "quality":
                system_prompt = "You are a code quality expert. Analyze the provided code for quality issues, best practices, and potential improvements."
            elif analysis_type == "security":
                system_prompt = "You are a security expert. Analyze the provided code for security vulnerabilities and potential risks."
            elif analysis_type == "performance":
                system_prompt = "You are a performance optimization expert. Analyze the provided code for performance issues and optimization opportunities."
            else:
                system_prompt = "You are a senior software engineer. Analyze the provided code and provide insights about its structure, functionality, and potential improvements."
            
            user_prompt = f"Please analyze this code:\n\n{context.get_formatted_context()}"
            
            # Get AI response
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            result = await self.openrouter_client.create_completion_with_retry(
                messages,
                model or self.openrouter_client.get_model_for_type(ModelType.CODING),
                temperature=0.3
            )
            
            if "error" in result:
                return {"success": False, "error": result["error"]}
            
            analysis = result["choices"][0]["message"]["content"]
            
            return {
                "success": True,
                "analysis": analysis,
                "analysis_type": analysis_type,
                "file_path": file_path,
                "context_stats": {
                    "total_tokens": context.total_tokens,
                    "total_items": len(context.items),
                    "truncated": context.truncated
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze code: {e}")
            return {"success": False, "error": str(e)}
    
    async def generate_code(
        self,
        project_id: int,
        target_file: str,
        description: str,
        code_type: str = "function",
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate code based on description"""
        try:
            # Get context for code generation
            context = await self.context_service.get_context_for_code_generation(
                project_id, target_file, description
            )
            
            # Prepare prompt
            system_prompt = f"""You are an expert programmer. Generate {code_type} code based on the description and context provided.
            
Rules:
1. Follow the coding style and patterns shown in the context
2. Use appropriate imports and dependencies
3. Include proper error handling
4. Add clear comments and docstrings
5. Ensure the code integrates well with the existing codebase
6. Return only the code, no explanations unless requested"""
            
            user_prompt = f"""Context from the codebase:
{context.get_formatted_context()}

Generate {code_type} with the following description:
{description}"""
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            result = await self.openrouter_client.create_completion_with_retry(
                messages,
                model or self.openrouter_client.get_model_for_type(ModelType.CODING),
                temperature=0.7
            )
            
            if "error" in result:
                return {"success": False, "error": result["error"]}
            
            generated_code = result["choices"][0]["message"]["content"]
            
            return {
                "success": True,
                "generated_code": generated_code,
                "description": description,
                "target_file": target_file,
                "code_type": code_type,
                "context_stats": {
                    "total_tokens": context.total_tokens,
                    "total_items": len(context.items)
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to generate code: {e}")
            return {"success": False, "error": str(e)}
    
    async def debug_error(
        self,
        project_id: int,
        error_message: str,
        file_path: Optional[str] = None,
        stack_trace: Optional[str] = None,
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """Debug an error with AI assistance"""
        try:
            # Get context for error debugging
            context = await self.context_service.get_context_for_error(
                project_id, error_message, file_path
            )
            
            # Prepare prompt
            system_prompt = """You are an expert debugger. Analyze the error and provide:
1. Root cause analysis
2. Specific fix recommendations
3. Code changes needed
4. Prevention strategies for similar errors

Be specific and actionable in your recommendations."""
            
            error_info = f"Error: {error_message}"
            if stack_trace:
                error_info += f"\n\nStack trace:\n{stack_trace}"
            
            user_prompt = f"""{error_info}

Context from the codebase:
{context.get_formatted_context()}

Please analyze this error and provide debugging assistance."""
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            result = await self.openrouter_client.create_completion_with_retry(
                messages,
                model or self.openrouter_client.get_model_for_type(ModelType.CODING),
                temperature=0.3
            )
            
            if "error" in result:
                return {"success": False, "error": result["error"]}
            
            debug_analysis = result["choices"][0]["message"]["content"]
            
            return {
                "success": True,
                "debug_analysis": debug_analysis,
                "error_message": error_message,
                "file_path": file_path,
                "context_stats": {
                    "total_tokens": context.total_tokens,
                    "total_items": len(context.items)
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to debug error: {e}")
            return {"success": False, "error": str(e)}
    
    async def refactor_code(
        self,
        project_id: int,
        file_path: str,
        refactor_type: str = "improve",
        target_symbol: Optional[str] = None,
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """Refactor code with AI assistance"""
        try:
            # Get context for refactoring
            context = await self.context_service.get_context_for_refactoring(
                project_id, file_path, target_symbol
            )
            
            # Prepare prompt based on refactor type
            if refactor_type == "optimize":
                system_prompt = "You are a performance optimization expert. Refactor the code to improve performance while maintaining functionality."
            elif refactor_type == "clean":
                system_prompt = "You are a clean code expert. Refactor the code to improve readability, maintainability, and follow best practices."
            elif refactor_type == "modernize":
                system_prompt = "You are a modern programming expert. Refactor the code to use modern language features and patterns."
            else:
                system_prompt = "You are a senior software engineer. Refactor the code to improve its overall quality and maintainability."
            
            user_prompt = f"""Please refactor this code ({refactor_type}):

{context.get_formatted_context()}

Provide the refactored code with explanations of the changes made."""
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            result = await self.openrouter_client.create_completion_with_retry(
                messages,
                model or self.openrouter_client.get_model_for_type(ModelType.CODING),
                temperature=0.5
            )
            
            if "error" in result:
                return {"success": False, "error": result["error"]}
            
            refactored_code = result["choices"][0]["message"]["content"]
            
            return {
                "success": True,
                "refactored_code": refactored_code,
                "refactor_type": refactor_type,
                "file_path": file_path,
                "target_symbol": target_symbol,
                "context_stats": {
                    "total_tokens": context.total_tokens,
                    "total_items": len(context.items)
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to refactor code: {e}")
            return {"success": False, "error": str(e)}
    
    async def chat_with_codebase(
        self,
        project_id: int,
        question: str,
        conversation_id: Optional[str] = None,
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """Chat about the codebase with AI"""
        try:
            # Generate conversation ID if not provided
            if not conversation_id:
                conversation_id = str(uuid.uuid4())
            
            # Get context for the question
            context = await self.context_service.get_context_for_query(
                project_id, question
            )
            
            # Check if this is a new conversation
            conversation = self.openrouter_client.get_conversation(conversation_id)
            if not conversation:
                # Create new conversation with system prompt
                conversation = self.openrouter_client.create_conversation(
                    conversation_id,
                    model or self.openrouter_client.get_model_for_type(ModelType.CODING)
                )
                
                system_prompt = f"""You are an AI assistant that helps developers understand and work with their codebase.
                
Context from the codebase:
{context.get_formatted_context()}

Answer questions about the code, explain functionality, suggest improvements, and help with development tasks."""
                
                conversation.add_message("system", system_prompt)
            
            # Send user question
            result = await self.openrouter_client.send_message(
                conversation_id,
                question,
                model=model or conversation.model
            )
            
            if "error" in result:
                return {"success": False, "error": result["error"]}
            
            response = result["choices"][0]["message"]["content"]
            
            return {
                "success": True,
                "response": response,
                "conversation_id": conversation_id,
                "question": question,
                "context_stats": {
                    "total_tokens": context.total_tokens,
                    "total_items": len(context.items)
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to chat with codebase: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_ai_status(self) -> Dict[str, Any]:
        """Get AI service status"""
        try:
            # Check OpenRouter availability
            openrouter_available = await self.openrouter_client.is_available()
            
            # Get available models
            models = await self.openrouter_client.get_available_models()
            
            # Get conversation stats
            conversation_stats = self.openrouter_client.get_conversation_stats()
            
            return {
                "openrouter_available": openrouter_available,
                "available_models": len(models),
                "default_models": {
                    "coding": self.openrouter_client.default_coding_model,
                    "vision": self.openrouter_client.default_vision_model
                },
                "conversation_stats": conversation_stats
            }
            
        except Exception as e:
            logger.error(f"Failed to get AI status: {e}")
            return {"error": str(e)}

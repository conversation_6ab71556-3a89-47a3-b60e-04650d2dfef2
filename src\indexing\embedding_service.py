"""
Embedding service using Ollama for local embeddings
"""
import asyncio
import hashlib
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
import httpx

from src.config.logging_config import get_logger
from config.settings import settings

logger = get_logger(__name__)


@dataclass
class EmbeddingResult:
    """Result of embedding generation"""
    text: str
    embedding: List[float]
    model: str
    dimensions: int
    hash: str


class OllamaEmbeddingService:
    """Service for generating embeddings using Ollama"""
    
    def __init__(self):
        self.base_url = settings.ollama_url
        self.model = settings.ollama_model
        self.client = httpx.AsyncClient(timeout=60.0)
        self._embedding_cache: Dict[str, EmbeddingResult] = {}
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def is_available(self) -> bool:
        """Check if Ollama service is available"""
        try:
            response = await self.client.get(f"{self.base_url}/api/tags")
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"Ollama service not available: {e}")
            return False
    
    async def ensure_model_available(self) -> bool:
        """Ensure the embedding model is available"""
        try:
            # Check if model is already available
            response = await self.client.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                models = response.json().get("models", [])
                for model in models:
                    if model.get("name", "").startswith(self.model):
                        logger.debug(f"Model {self.model} is available")
                        return True
            
            # Try to pull the model
            logger.info(f"Pulling embedding model: {self.model}")
            response = await self.client.post(
                f"{self.base_url}/api/pull",
                json={"name": self.model},
                timeout=300.0  # 5 minutes for model download
            )
            
            if response.status_code == 200:
                logger.info(f"Successfully pulled model: {self.model}")
                return True
            else:
                logger.error(f"Failed to pull model {self.model}: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error ensuring model availability: {e}")
            return False
    
    def _get_text_hash(self, text: str) -> str:
        """Generate hash for text to use as cache key"""
        return hashlib.sha256(text.encode()).hexdigest()[:16]
    
    async def generate_embedding(self, text: str, use_cache: bool = True) -> Optional[EmbeddingResult]:
        """Generate embedding for text"""
        if not text.strip():
            return None
        
        text_hash = self._get_text_hash(text)
        
        # Check cache
        if use_cache and text_hash in self._embedding_cache:
            logger.debug(f"Using cached embedding for text hash: {text_hash}")
            return self._embedding_cache[text_hash]
        
        try:
            # Generate embedding using Ollama
            response = await self.client.post(
                f"{self.base_url}/api/embeddings",
                json={
                    "model": self.model,
                    "prompt": text
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                embedding = data.get("embedding", [])
                
                if embedding:
                    result = EmbeddingResult(
                        text=text,
                        embedding=embedding,
                        model=self.model,
                        dimensions=len(embedding),
                        hash=text_hash
                    )
                    
                    # Cache the result
                    if use_cache:
                        self._embedding_cache[text_hash] = result
                    
                    logger.debug(f"Generated embedding with {len(embedding)} dimensions")
                    return result
                else:
                    logger.error("Empty embedding returned from Ollama")
                    return None
            else:
                logger.error(f"Ollama embedding request failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            return None
    
    async def generate_embeddings_batch(self, texts: List[str], use_cache: bool = True) -> List[Optional[EmbeddingResult]]:
        """Generate embeddings for multiple texts"""
        tasks = []
        for text in texts:
            task = self.generate_embedding(text, use_cache)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Failed to generate embedding for text {i}: {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)
        
        return processed_results
    
    def clear_cache(self):
        """Clear embedding cache"""
        self._embedding_cache.clear()
        logger.info("Embedding cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            "cache_size": len(self._embedding_cache),
            "model": self.model,
            "base_url": self.base_url
        }


class CodeChunker:
    """Chunks code into meaningful segments for embedding"""
    
    def __init__(self, max_chunk_size: int = 1000, overlap_size: int = 100):
        self.max_chunk_size = max_chunk_size
        self.overlap_size = overlap_size
    
    def chunk_by_lines(self, code: str, file_path: str = "") -> List[Dict[str, Any]]:
        """Chunk code by lines with overlap"""
        lines = code.split('\n')
        chunks = []
        
        if len(lines) <= self.max_chunk_size // 50:  # Rough estimate: 50 chars per line
            # Small file, return as single chunk
            chunks.append({
                "text": code,
                "start_line": 1,
                "end_line": len(lines),
                "file_path": file_path,
                "chunk_type": "full_file"
            })
        else:
            # Split into chunks
            lines_per_chunk = self.max_chunk_size // 50
            overlap_lines = self.overlap_size // 50
            
            start = 0
            chunk_id = 0
            
            while start < len(lines):
                end = min(start + lines_per_chunk, len(lines))
                chunk_lines = lines[start:end]
                
                chunks.append({
                    "text": '\n'.join(chunk_lines),
                    "start_line": start + 1,
                    "end_line": end,
                    "file_path": file_path,
                    "chunk_type": "lines",
                    "chunk_id": chunk_id
                })
                
                # Move start position with overlap
                start = end - overlap_lines if end < len(lines) else end
                chunk_id += 1
        
        return chunks
    
    def chunk_by_functions(self, code: str, symbols: List[Dict[str, Any]], file_path: str = "") -> List[Dict[str, Any]]:
        """Chunk code by functions and classes"""
        chunks = []
        lines = code.split('\n')
        
        # Sort symbols by start line
        sorted_symbols = sorted(symbols, key=lambda x: x.get('start_line', 0))
        
        for symbol in sorted_symbols:
            start_line = symbol.get('start_line', 1) - 1  # Convert to 0-based
            end_line = symbol.get('end_line', len(lines)) - 1
            
            if start_line < len(lines) and end_line <= len(lines):
                symbol_lines = lines[start_line:end_line + 1]
                symbol_text = '\n'.join(symbol_lines)
                
                chunks.append({
                    "text": symbol_text,
                    "start_line": start_line + 1,
                    "end_line": end_line + 1,
                    "file_path": file_path,
                    "chunk_type": "symbol",
                    "symbol_name": symbol.get('name', 'unknown'),
                    "symbol_type": symbol.get('type', 'unknown')
                })
        
        return chunks
    
    def chunk_code(self, code: str, symbols: List[Dict[str, Any]] = None, file_path: str = "") -> List[Dict[str, Any]]:
        """Chunk code using the best strategy"""
        chunks = []
        
        # If we have symbol information, use function-based chunking
        if symbols:
            symbol_chunks = self.chunk_by_functions(code, symbols, file_path)
            chunks.extend(symbol_chunks)
        
        # Always add line-based chunks for comprehensive coverage
        line_chunks = self.chunk_by_lines(code, file_path)
        chunks.extend(line_chunks)
        
        return chunks


class HybridSearchService:
    """Combines semantic and keyword search"""
    
    def __init__(self, embedding_service: OllamaEmbeddingService):
        self.embedding_service = embedding_service
    
    def calculate_text_similarity(self, query: str, text: str) -> float:
        """Calculate simple text similarity score"""
        query_words = set(query.lower().split())
        text_words = set(text.lower().split())
        
        if not query_words or not text_words:
            return 0.0
        
        intersection = query_words.intersection(text_words)
        union = query_words.union(text_words)
        
        return len(intersection) / len(union) if union else 0.0
    
    def calculate_semantic_similarity(self, query_embedding: List[float], text_embedding: List[float]) -> float:
        """Calculate cosine similarity between embeddings"""
        if not query_embedding or not text_embedding:
            return 0.0
        
        # Cosine similarity
        dot_product = sum(a * b for a, b in zip(query_embedding, text_embedding))
        magnitude_a = sum(a * a for a in query_embedding) ** 0.5
        magnitude_b = sum(b * b for b in text_embedding) ** 0.5
        
        if magnitude_a == 0 or magnitude_b == 0:
            return 0.0
        
        return dot_product / (magnitude_a * magnitude_b)
    
    async def hybrid_search(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        semantic_weight: float = 0.7,
        text_weight: float = 0.3
    ) -> List[Tuple[Dict[str, Any], float]]:
        """Perform hybrid search combining semantic and text similarity"""
        
        # Generate query embedding
        query_embedding_result = await self.embedding_service.generate_embedding(query)
        query_embedding = query_embedding_result.embedding if query_embedding_result else []
        
        results = []
        
        for doc in documents:
            text = doc.get('text', '')
            doc_embedding = doc.get('embedding', [])
            
            # Calculate text similarity
            text_score = self.calculate_text_similarity(query, text)
            
            # Calculate semantic similarity
            semantic_score = 0.0
            if query_embedding and doc_embedding:
                semantic_score = self.calculate_semantic_similarity(query_embedding, doc_embedding)
            
            # Combine scores
            combined_score = (semantic_weight * semantic_score) + (text_weight * text_score)
            
            results.append((doc, combined_score))
        
        # Sort by combined score
        results.sort(key=lambda x: x[1], reverse=True)
        
        return results

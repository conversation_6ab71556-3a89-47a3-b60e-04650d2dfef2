"""
File service for managing files
"""
from pathlib import Path
from typing import List, Optional
from sqlalchemy.orm import Session

from src.models.file import File
from src.models.project import Project
from src.config.logging_config import get_logger

logger = get_logger(__name__)


class FileService:
    """Service for managing files"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def list_project_files(
        self, 
        project_id: int, 
        skip: int = 0, 
        limit: int = 1000,
        extension: Optional[str] = None
    ) -> List[File]:
        """List files in a project"""
        query = self.db.query(File).filter(File.project_id == project_id)
        
        if extension:
            query = query.filter(File.extension == extension)
        
        return query.offset(skip).limit(limit).all()
    
    def get_file(self, file_id: int) -> Optional[File]:
        """Get file by ID"""
        return self.db.query(File).filter(File.id == file_id).first()
    
    def get_file_content(self, file_id: int) -> Optional[str]:
        """Get file content"""
        file = self.get_file(file_id)
        if not file:
            return None
        
        # Get project to construct full path
        project = self.db.query(Project).filter(Project.id == file.project_id).first()
        if not project:
            return None
        
        file_path = Path(project.path) / file.path
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error("Failed to read file content", file_id=file_id, error=str(e))
            return None
    
    def update_file_content(self, file_id: int, content: str) -> bool:
        """Update file content"""
        file = self.get_file(file_id)
        if not file:
            return False
        
        # Get project to construct full path
        project = self.db.query(Project).filter(Project.id == file.project_id).first()
        if not project:
            return False
        
        file_path = Path(project.path) / file.path
        
        try:
            # Create backup
            backup_path = file_path.with_suffix(file_path.suffix + '.backup')
            if file_path.exists():
                import shutil
                shutil.copy2(file_path, backup_path)
            
            # Write new content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Update file metadata
            import hashlib
            file.content_hash = hashlib.sha256(content.encode()).hexdigest()
            file.size = len(content.encode())
            
            self.db.commit()
            
            logger.info("File content updated", file_id=file_id)
            return True
            
        except Exception as e:
            logger.error("Failed to update file content", file_id=file_id, error=str(e))
            return False
    
    def delete_file(self, file_id: int) -> bool:
        """Delete file"""
        file = self.get_file(file_id)
        if not file:
            return False
        
        self.db.delete(file)
        self.db.commit()
        
        logger.info("File deleted from database", file_id=file_id)
        return True
    
    async def search_files(
        self,
        query: str,
        project_id: Optional[int] = None,
        file_types: Optional[List[str]] = None,
        limit: int = 50
    ) -> List[dict]:
        """Search files by content or metadata"""
        # TODO: Implement vector search using ChromaDB
        # For now, return basic filename search
        
        db_query = self.db.query(File)
        
        if project_id:
            db_query = db_query.filter(File.project_id == project_id)
        
        if file_types:
            db_query = db_query.filter(File.extension.in_(file_types))
        
        # Simple name search for now
        files = db_query.filter(File.name.contains(query)).limit(limit).all()
        
        results = []
        for file in files:
            results.append({
                "file_id": file.id,
                "project_id": file.project_id,
                "path": file.path,
                "name": file.name,
                "extension": file.extension,
                "relevance_score": 1.0  # TODO: Calculate actual relevance
            })
        
        return results

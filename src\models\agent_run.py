"""
Agent run database model
"""
from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from pydantic import BaseModel

from src.config.database import Base


class AgentRun(Base):
    """Agent run database model"""
    __tablename__ = "agent_runs"
    
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, index=True)
    agent_type = Column(String(100), nullable=False, index=True)
    status = Column(String(50), nullable=False, index=True, default="running")  # running, paused, completed, failed, stopped
    config = Column(JSON, nullable=False, default=dict)
    started_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    completed_at = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)
    iterations = Column(Integer, default=0, nullable=False)
    
    # Relationships
    project = relationship("Project", back_populates="agent_runs")
    error_logs = relationship("ErrorLog", back_populates="agent_run", cascade="all, delete-orphan")


class AgentRunCreate(BaseModel):
    """Agent run creation schema"""
    project_id: int
    agent_type: str
    config: Dict[str, Any] = {}


class AgentRunUpdate(BaseModel):
    """Agent run update schema"""
    status: Optional[str] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    iterations: Optional[int] = None


class AgentRunResponse(BaseModel):
    """Agent run response schema"""
    id: int
    project_id: int
    agent_type: str
    status: str
    config: Dict[str, Any]
    started_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    iterations: int
    
    class Config:
        from_attributes = True

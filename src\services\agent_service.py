"""
Agent service for managing AI agents
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session

from src.models.agent_run import Agent<PERSON><PERSON>, AgentRunCreate, AgentRunUpdate
from src.models.project import Project
from src.config.logging_config import get_logger

logger = get_logger(__name__)


class AgentService:
    """Service for managing AI agents"""
    
    def __init__(self, db: Optional[Session] = None):
        self.db = db
    
    def list_agent_runs(
        self,
        project_id: Optional[int] = None,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[AgentRun]:
        """List agent runs"""
        if not self.db:
            return []
        
        query = self.db.query(AgentRun)
        
        if project_id:
            query = query.filter(AgentRun.project_id == project_id)
        
        if status:
            query = query.filter(AgentRun.status == status)
        
        return query.order_by(AgentRun.started_at.desc()).offset(skip).limit(limit).all()
    
    def get_agent_run(self, run_id: int) -> Optional[AgentRun]:
        """Get agent run by ID"""
        if not self.db:
            return None
        
        return self.db.query(AgentRun).filter(AgentRun.id == run_id).first()
    
    async def start_agent(
        self,
        project_id: int,
        agent_type: str = "continuous_coder",
        config: Dict[str, Any] = None
    ) -> AgentRun:
        """Start a new agent run"""
        if not self.db:
            raise ValueError("Database session required")
        
        # Validate project exists
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project:
            raise ValueError("Project not found")
        
        # Check if there's already a running agent for this project
        existing_run = (
            self.db.query(AgentRun)
            .filter(
                AgentRun.project_id == project_id,
                AgentRun.status.in_(["running", "paused"])
            )
            .first()
        )
        
        if existing_run:
            raise ValueError("Agent already running for this project")
        
        # Create new agent run
        agent_run = AgentRun(
            project_id=project_id,
            agent_type=agent_type,
            config=config or {},
            status="running"
        )
        
        self.db.add(agent_run)
        self.db.commit()
        self.db.refresh(agent_run)
        
        # TODO: Start actual agent execution in background
        logger.info("Agent run started", run_id=agent_run.id, project_id=project_id, agent_type=agent_type)
        
        return agent_run
    
    async def control_agent(self, run_id: int, action: str) -> Dict[str, Any]:
        """Control agent execution"""
        if not self.db:
            raise ValueError("Database session required")
        
        agent_run = self.get_agent_run(run_id)
        if not agent_run:
            raise ValueError("Agent run not found")
        
        if action == "pause":
            if agent_run.status != "running":
                raise ValueError("Can only pause running agents")
            agent_run.status = "paused"
            
        elif action == "resume":
            if agent_run.status != "paused":
                raise ValueError("Can only resume paused agents")
            agent_run.status = "running"
            
        elif action == "stop":
            if agent_run.status not in ["running", "paused"]:
                raise ValueError("Can only stop running or paused agents")
            agent_run.status = "stopped"
            agent_run.completed_at = datetime.utcnow()
            
        else:
            raise ValueError(f"Unknown action: {action}")
        
        self.db.commit()
        
        # TODO: Send control signal to actual agent
        
        return {
            "run_id": run_id,
            "action": action,
            "new_status": agent_run.status,
            "message": f"Agent {action} successful"
        }
    
    def get_agent_logs(self, run_id: int, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """Get agent run logs"""
        # TODO: Implement actual log retrieval
        # For now, return placeholder logs
        return [
            {
                "timestamp": datetime.utcnow().isoformat(),
                "level": "INFO",
                "message": f"Agent run {run_id} log entry",
                "iteration": 1
            }
        ]
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall agent system status"""
        if not self.db:
            return {
                "total_runs": 0,
                "active_runs": 0,
                "completed_runs": 0,
                "failed_runs": 0
            }
        
        total_runs = self.db.query(AgentRun).count()
        active_runs = self.db.query(AgentRun).filter(AgentRun.status.in_(["running", "paused"])).count()
        completed_runs = self.db.query(AgentRun).filter(AgentRun.status == "completed").count()
        failed_runs = self.db.query(AgentRun).filter(AgentRun.status == "failed").count()
        
        return {
            "total_runs": total_runs,
            "active_runs": active_runs,
            "completed_runs": completed_runs,
            "failed_runs": failed_runs,
            "system_healthy": True
        }

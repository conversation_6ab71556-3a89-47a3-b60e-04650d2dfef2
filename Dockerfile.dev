# Development Dockerfile for EchoCode
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    git \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy Python requirements and install basic dependencies
COPY requirements.txt .

# Install only essential dependencies for basic functionality
RUN pip install --no-cache-dir \
    fastapi==0.104.1 \
    uvicorn[standard]==0.24.0 \
    python-multipart==0.0.6 \
    jinja2==3.1.2 \
    sqlalchemy==2.0.23 \
    alembic==1.12.1 \
    redis==5.0.1 \
    httpx==0.25.2 \
    pydantic==2.5.0 \
    pydantic-settings==2.1.0 \
    structlog==23.2.0 \
    rich==13.7.0 \
    python-dotenv==1.0.0 \
    click==8.1.7

# Create necessary directories
RUN mkdir -p /app/data /app/logs /app/projects /app/static

# Copy source code
COPY config/ ./config/
COPY src/ ./src/
COPY main.py .

# Create a simple index.html for testing
RUN echo '<!DOCTYPE html><html><head><title>EchoCode</title></head><body><h1>EchoCode AI Coder</h1><p>Backend is running! Frontend will be built separately.</p><p><a href="/docs">API Documentation</a></p></body></html>' > /app/static/index.html

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

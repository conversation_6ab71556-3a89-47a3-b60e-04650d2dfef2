"""
Projects API endpoints
"""
from typing import List, Optional
from pathlib import Path

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel

from src.config.database import get_database
from src.models.project import Project, ProjectCreate, ProjectUpdate
from src.services.project_service import ProjectService
from src.config.logging_config import get_logger

logger = get_logger(__name__)
router = APIRouter()


class ProjectResponse(BaseModel):
    id: int
    name: str
    path: str
    description: Optional[str] = None
    is_active: bool
    created_at: str
    updated_at: str
    file_count: int = 0
    
    class Config:
        from_attributes = True


@router.get("/", response_model=List[ProjectResponse])
async def list_projects(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_database)
):
    """List all projects"""
    try:
        service = ProjectService(db)
        projects = service.list_projects(skip=skip, limit=limit)
        # Convert datetime fields to strings
        result = []
        for project in projects:
            project_dict = {
                "id": project.id,
                "name": project.name,
                "path": project.path,
                "description": project.description,
                "is_active": project.is_active,
                "created_at": project.created_at.isoformat(),
                "updated_at": project.updated_at.isoformat(),
                "file_count": getattr(project, 'file_count', 0)
            }
            result.append(project_dict)
        return result
    except Exception as e:
        logger.error("Failed to list projects", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list projects"
        )


@router.post("/", response_model=ProjectResponse)
async def create_project(
    project_data: ProjectCreate,
    db: Session = Depends(get_database)
):
    """Create a new project"""
    try:
        service = ProjectService(db)
        project = service.create_project(project_data)
        logger.info("Project created", project_id=project.id, name=project.name)
        # Convert datetime fields to strings
        result = {
            "id": project.id,
            "name": project.name,
            "path": project.path,
            "description": project.description,
            "is_active": project.is_active,
            "created_at": project.created_at.isoformat(),
            "updated_at": project.updated_at.isoformat(),
            "file_count": getattr(project, 'file_count', 0)
        }
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to create project", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create project"
        )


@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: int,
    db: Session = Depends(get_database)
):
    """Get project by ID"""
    try:
        service = ProjectService(db)
        project = service.get_project(project_id)
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )
        return project
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get project", project_id=project_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get project"
        )


@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: int,
    project_data: ProjectUpdate,
    db: Session = Depends(get_database)
):
    """Update project"""
    try:
        service = ProjectService(db)
        project = service.update_project(project_id, project_data)
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )
        logger.info("Project updated", project_id=project_id)
        return project
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update project", project_id=project_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update project"
        )


@router.delete("/{project_id}")
async def delete_project(
    project_id: int,
    db: Session = Depends(get_database)
):
    """Delete project"""
    try:
        service = ProjectService(db)
        success = service.delete_project(project_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )
        logger.info("Project deleted", project_id=project_id)
        return {"message": "Project deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete project", project_id=project_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete project"
        )


@router.post("/{project_id}/scan")
async def scan_project(
    project_id: int,
    db: Session = Depends(get_database)
):
    """Scan project for files and update index"""
    try:
        service = ProjectService(db)
        result = await service.scan_project(project_id)
        logger.info("Project scanned", project_id=project_id, files_found=result["files_found"])
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to scan project", project_id=project_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to scan project"
        )

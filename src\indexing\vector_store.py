"""
Vector storage using ChromaDB for semantic search
"""
import uuid
from typing import List, Dict, Optional, Any, Tuple
from pathlib import Path
import json

try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False

from src.indexing.embedding_service import EmbeddingResult
from src.config.logging_config import get_logger
from config.settings import settings

logger = get_logger(__name__)


class VectorStore:
    """ChromaDB-based vector storage for code embeddings"""
    
    def __init__(self, persist_directory: Optional[str] = None):
        self.persist_directory = persist_directory or settings.chroma_persist_directory
        self.client = None
        self.collections: Dict[str, Any] = {}
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize ChromaDB client"""
        if not CHROMADB_AVAILABLE:
            logger.warning("ChromaDB not available. Vector storage will be disabled.")
            return
        
        try:
            # Ensure persist directory exists
            Path(self.persist_directory).mkdir(parents=True, exist_ok=True)
            
            # Initialize ChromaDB client
            self.client = chromadb.PersistentClient(
                path=self.persist_directory,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            logger.info(f"ChromaDB client initialized with persist directory: {self.persist_directory}")
            
        except Exception as e:
            logger.error(f"Failed to initialize ChromaDB client: {e}")
            self.client = None
    
    def is_available(self) -> bool:
        """Check if vector store is available"""
        return CHROMADB_AVAILABLE and self.client is not None
    
    def get_or_create_collection(self, project_id: int, collection_type: str = "code") -> Optional[Any]:
        """Get or create a collection for a project"""
        if not self.is_available():
            return None
        
        collection_name = f"project_{project_id}_{collection_type}"
        
        if collection_name in self.collections:
            return self.collections[collection_name]
        
        try:
            # Try to get existing collection
            collection = self.client.get_collection(name=collection_name)
            logger.debug(f"Retrieved existing collection: {collection_name}")
        except Exception:
            # Create new collection
            try:
                collection = self.client.create_collection(
                    name=collection_name,
                    metadata={"project_id": project_id, "type": collection_type}
                )
                logger.info(f"Created new collection: {collection_name}")
            except Exception as e:
                logger.error(f"Failed to create collection {collection_name}: {e}")
                return None
        
        self.collections[collection_name] = collection
        return collection
    
    def add_embeddings(
        self,
        project_id: int,
        embeddings: List[EmbeddingResult],
        metadata_list: List[Dict[str, Any]],
        collection_type: str = "code"
    ) -> bool:
        """Add embeddings to the vector store"""
        if not self.is_available():
            logger.warning("Vector store not available")
            return False
        
        collection = self.get_or_create_collection(project_id, collection_type)
        if not collection:
            return False
        
        try:
            # Prepare data for ChromaDB
            ids = []
            embeddings_data = []
            documents = []
            metadatas = []
            
            for i, (embedding_result, metadata) in enumerate(zip(embeddings, metadata_list)):
                # Generate unique ID
                doc_id = metadata.get('id') or str(uuid.uuid4())
                ids.append(doc_id)
                
                # Add embedding
                embeddings_data.append(embedding_result.embedding)
                
                # Add document text
                documents.append(embedding_result.text)
                
                # Prepare metadata
                meta = {
                    "file_path": metadata.get("file_path", ""),
                    "start_line": metadata.get("start_line", 0),
                    "end_line": metadata.get("end_line", 0),
                    "chunk_type": metadata.get("chunk_type", "unknown"),
                    "symbol_name": metadata.get("symbol_name", ""),
                    "symbol_type": metadata.get("symbol_type", ""),
                    "language": metadata.get("language", ""),
                    "embedding_model": embedding_result.model,
                    "embedding_hash": embedding_result.hash
                }
                metadatas.append(meta)
            
            # Add to collection
            collection.add(
                ids=ids,
                embeddings=embeddings_data,
                documents=documents,
                metadatas=metadatas
            )
            
            logger.info(f"Added {len(embeddings)} embeddings to collection for project {project_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add embeddings to vector store: {e}")
            return False
    
    def search_similar(
        self,
        project_id: int,
        query_embedding: List[float],
        n_results: int = 10,
        collection_type: str = "code",
        where_filter: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar embeddings"""
        if not self.is_available():
            return []
        
        collection = self.get_or_create_collection(project_id, collection_type)
        if not collection:
            return []
        
        try:
            # Perform similarity search
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where=where_filter,
                include=["documents", "metadatas", "distances"]
            )
            
            # Format results
            formatted_results = []
            if results and results.get("documents") and len(results["documents"]) > 0:
                documents = results["documents"][0]
                metadatas = results["metadatas"][0]
                distances = results["distances"][0]
                
                for doc, metadata, distance in zip(documents, metadatas, distances):
                    formatted_results.append({
                        "text": doc,
                        "metadata": metadata,
                        "similarity_score": 1.0 - distance,  # Convert distance to similarity
                        "distance": distance
                    })
            
            logger.debug(f"Found {len(formatted_results)} similar embeddings")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to search similar embeddings: {e}")
            return []
    
    def search_by_text(
        self,
        project_id: int,
        query_text: str,
        n_results: int = 10,
        collection_type: str = "code",
        where_filter: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search by text query (requires embedding generation)"""
        # This method would need the embedding service to generate query embedding
        # For now, return empty list
        logger.warning("Text search requires embedding service integration")
        return []
    
    def delete_embeddings(
        self,
        project_id: int,
        file_path: str,
        collection_type: str = "code"
    ) -> bool:
        """Delete embeddings for a specific file"""
        if not self.is_available():
            return False
        
        collection = self.get_or_create_collection(project_id, collection_type)
        if not collection:
            return False
        
        try:
            # Delete embeddings where file_path matches
            collection.delete(
                where={"file_path": file_path}
            )
            
            logger.info(f"Deleted embeddings for file: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete embeddings for file {file_path}: {e}")
            return False
    
    def clear_project_embeddings(self, project_id: int, collection_type: str = "code") -> bool:
        """Clear all embeddings for a project"""
        if not self.is_available():
            return False
        
        collection_name = f"project_{project_id}_{collection_type}"
        
        try:
            # Delete the entire collection
            self.client.delete_collection(name=collection_name)
            
            # Remove from cache
            if collection_name in self.collections:
                del self.collections[collection_name]
            
            logger.info(f"Cleared all embeddings for project {project_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear embeddings for project {project_id}: {e}")
            return False
    
    def get_collection_stats(self, project_id: int, collection_type: str = "code") -> Dict[str, Any]:
        """Get statistics for a collection"""
        if not self.is_available():
            return {}
        
        collection = self.get_or_create_collection(project_id, collection_type)
        if not collection:
            return {}
        
        try:
            count = collection.count()
            
            return {
                "project_id": project_id,
                "collection_type": collection_type,
                "total_embeddings": count,
                "persist_directory": self.persist_directory
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {}
    
    def list_collections(self) -> List[Dict[str, Any]]:
        """List all collections"""
        if not self.is_available():
            return []
        
        try:
            collections = self.client.list_collections()
            
            result = []
            for collection in collections:
                result.append({
                    "name": collection.name,
                    "metadata": collection.metadata,
                    "count": collection.count()
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to list collections: {e}")
            return []
    
    def backup_collection(self, project_id: int, backup_path: str, collection_type: str = "code") -> bool:
        """Backup a collection to file"""
        if not self.is_available():
            return False
        
        collection = self.get_or_create_collection(project_id, collection_type)
        if not collection:
            return False
        
        try:
            # Get all data from collection
            results = collection.get(include=["documents", "metadatas", "embeddings"])
            
            # Save to file
            backup_data = {
                "project_id": project_id,
                "collection_type": collection_type,
                "data": results
            }
            
            with open(backup_path, 'w') as f:
                json.dump(backup_data, f, indent=2)
            
            logger.info(f"Backed up collection to: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to backup collection: {e}")
            return False
    
    def restore_collection(self, backup_path: str) -> bool:
        """Restore a collection from backup file"""
        if not self.is_available():
            return False
        
        try:
            with open(backup_path, 'r') as f:
                backup_data = json.load(f)
            
            project_id = backup_data["project_id"]
            collection_type = backup_data["collection_type"]
            data = backup_data["data"]
            
            # Clear existing collection
            self.clear_project_embeddings(project_id, collection_type)
            
            # Create new collection
            collection = self.get_or_create_collection(project_id, collection_type)
            if not collection:
                return False
            
            # Restore data
            if data.get("ids"):
                collection.add(
                    ids=data["ids"],
                    embeddings=data.get("embeddings", []),
                    documents=data.get("documents", []),
                    metadatas=data.get("metadatas", [])
                )
            
            logger.info(f"Restored collection from: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to restore collection: {e}")
            return False

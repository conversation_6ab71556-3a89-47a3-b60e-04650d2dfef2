# FastAPI and web framework
fastapi[standard]>=0.115.12,<0.116.0
uvicorn[standard]>=0.32.0,<0.33.0
python-multipart>=0.0.12,<0.1.0
jinja2>=3.1.4,<4.0.0

# Database and ORM
sqlalchemy>=2.0.36,<3.0.0
alembic>=1.14.0,<2.0.0
# sqlite3 is built into Python

# Vector database and embeddings
chromadb>=0.5.16,<0.6.0
sentence-transformers>=3.3.1,<4.0.0
numpy>=1.22.5,<2.0.0

# Code parsing and analysis
tree-sitter>=0.24.0,<0.25.0
tree-sitter-python>=0.23.6,<0.24.0
tree-sitter-javascript>=0.23.0,<0.24.0
tree-sitter-typescript>=0.23.0,<0.24.0
tree-sitter-java>=0.23.2,<0.24.0
tree-sitter-cpp>=0.23.4,<0.24.0
tree-sitter-c>=0.23.0,<0.24.0
tree-sitter-rust>=0.23.0,<0.24.0
tree-sitter-go>=0.23.3,<0.24.0

# Task queue and background processing
celery>=5.4.0,<6.0.0
redis>=5.2.0,<6.0.0

# HTTP clients and API integration
httpx>=0.28.1,<0.29.0
aiohttp>=3.11.10,<4.0.0
requests>=2.32.3,<3.0.0

# File watching and monitoring
watchdog==3.0.0

# JSON and data processing
pydantic>=2.10.3,<3.0.0
pydantic-settings>=2.7.0,<3.0.0
orjson>=3.10.12,<4.0.0

# Logging and monitoring
structlog==23.2.0
rich==13.7.0

# Security and authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Utilities
python-dotenv==1.0.0
click==8.1.7
pathlib2==2.3.7
typing-extensions>=4.12.0,<5.0.0

# Docker and containerization support
docker==6.1.3

# Git integration
GitPython==3.1.40

# Graph analysis
networkx==3.2.1

# Code quality and analysis
pylint==3.0.3
mypy==1.7.1

# WebSocket support for real-time updates
websockets==12.0

# System monitoring
psutil==5.9.6

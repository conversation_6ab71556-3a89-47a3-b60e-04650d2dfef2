"""
OpenRouter API client with retry logic, rate limiting, and conversation management
"""
import asyncio
import time
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass, field
from enum import Enum
import json
import httpx

from src.config.logging_config import get_logger
from config.settings import settings

logger = get_logger(__name__)


class ModelType(Enum):
    """Types of AI models"""
    CODING = "coding"
    VISION = "vision"
    EMBEDDING = "embedding"


@dataclass
class Message:
    """Chat message"""
    role: str  # system, user, assistant
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Conversation:
    """Conversation state"""
    id: str
    messages: List[Message] = field(default_factory=list)
    model: str = ""
    created_at: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_message(self, role: str, content: str, metadata: Dict[str, Any] = None):
        """Add a message to the conversation"""
        message = Message(role=role, content=content, metadata=metadata or {})
        self.messages.append(message)
    
    def get_messages_for_api(self) -> List[Dict[str, str]]:
        """Get messages in OpenRouter API format"""
        return [{"role": msg.role, "content": msg.content} for msg in self.messages]
    
    def get_token_count(self) -> int:
        """Estimate token count for the conversation"""
        total_chars = sum(len(msg.content) for msg in self.messages)
        return total_chars // 4  # Rough estimate: 4 chars per token


@dataclass
class RateLimiter:
    """Rate limiter for API requests"""
    requests_per_minute: int
    requests_per_day: int
    request_times: List[float] = field(default_factory=list)
    daily_count: int = 0
    last_reset: float = field(default_factory=time.time)
    
    def can_make_request(self) -> bool:
        """Check if we can make a request"""
        now = time.time()
        
        # Reset daily count if needed
        if now - self.last_reset > 86400:  # 24 hours
            self.daily_count = 0
            self.last_reset = now
        
        # Check daily limit
        if self.daily_count >= self.requests_per_day:
            return False
        
        # Clean old request times (older than 1 minute)
        self.request_times = [t for t in self.request_times if now - t < 60]
        
        # Check per-minute limit
        return len(self.request_times) < self.requests_per_minute
    
    def record_request(self):
        """Record a request"""
        now = time.time()
        self.request_times.append(now)
        self.daily_count += 1
    
    def get_wait_time(self) -> float:
        """Get time to wait before next request"""
        if not self.request_times:
            return 0.0
        
        now = time.time()
        oldest_request = min(self.request_times)
        
        # Wait until we can make another request
        return max(0.0, 60.0 - (now - oldest_request))


class OpenRouterClient:
    """OpenRouter API client with advanced features"""
    
    def __init__(self):
        self.api_key = settings.openrouter_api_key
        self.base_url = settings.openrouter_base_url
        self.default_coding_model = settings.default_coding_model
        self.default_vision_model = settings.default_vision_model
        
        # HTTP client
        self.client = httpx.AsyncClient(
            timeout=120.0,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://echocode.ai",
                "X-Title": "EchoCode AI Coder"
            }
        )
        
        # Rate limiting
        self.rate_limiter = RateLimiter(
            requests_per_minute=settings.openrouter_requests_per_minute,
            requests_per_day=settings.openrouter_requests_per_day
        )
        
        # Conversation management
        self.conversations: Dict[str, Conversation] = {}
        
        # Model information cache
        self.model_info_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_expiry = 0
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def is_available(self) -> bool:
        """Check if OpenRouter API is available"""
        try:
            response = await self.client.get(f"{self.base_url}/models")
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"OpenRouter API not available: {e}")
            return False
    
    async def get_available_models(self, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """Get list of available models"""
        now = time.time()
        
        # Use cache if available and not expired
        if not force_refresh and self.model_info_cache and now < self.cache_expiry:
            return list(self.model_info_cache.values())
        
        try:
            response = await self.client.get(f"{self.base_url}/models")
            
            if response.status_code == 200:
                models_data = response.json()
                models = models_data.get("data", [])
                
                # Update cache
                self.model_info_cache = {model["id"]: model for model in models}
                self.cache_expiry = now + 3600  # Cache for 1 hour
                
                logger.info(f"Retrieved {len(models)} available models")
                return models
            else:
                logger.error(f"Failed to get models: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting available models: {e}")
            return []
    
    def get_model_for_type(self, model_type: ModelType) -> str:
        """Get appropriate model for task type"""
        if model_type == ModelType.CODING:
            return self.default_coding_model
        elif model_type == ModelType.VISION:
            return self.default_vision_model
        else:
            return self.default_coding_model
    
    async def wait_for_rate_limit(self):
        """Wait if rate limited"""
        if not self.rate_limiter.can_make_request():
            wait_time = self.rate_limiter.get_wait_time()
            if wait_time > 0:
                logger.info(f"Rate limited, waiting {wait_time:.1f} seconds")
                await asyncio.sleep(wait_time)
    
    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Create a chat completion"""
        
        # Wait for rate limit
        await self.wait_for_rate_limit()
        
        # Use default model if not specified
        if not model:
            model = self.default_coding_model
        
        # Prepare request
        request_data = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "stream": stream,
            **kwargs
        }
        
        if max_tokens:
            request_data["max_tokens"] = max_tokens
        
        try:
            # Record request for rate limiting
            self.rate_limiter.record_request()
            
            # Make request
            response = await self.client.post(
                f"{self.base_url}/chat/completions",
                json=request_data
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.debug(f"Chat completion successful, model: {model}")
                return result
            else:
                error_msg = f"OpenRouter API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {"error": error_msg, "status_code": response.status_code}
                
        except Exception as e:
            error_msg = f"Failed to create chat completion: {e}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    async def create_chat_completion_stream(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Create a streaming chat completion"""
        
        # Wait for rate limit
        await self.wait_for_rate_limit()
        
        # Use default model if not specified
        if not model:
            model = self.default_coding_model
        
        # Prepare request
        request_data = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "stream": True,
            **kwargs
        }
        
        if max_tokens:
            request_data["max_tokens"] = max_tokens
        
        try:
            # Record request for rate limiting
            self.rate_limiter.record_request()
            
            # Make streaming request
            async with self.client.stream(
                "POST",
                f"{self.base_url}/chat/completions",
                json=request_data
            ) as response:
                
                if response.status_code != 200:
                    error_msg = f"OpenRouter API error: {response.status_code}"
                    logger.error(error_msg)
                    yield {"error": error_msg, "status_code": response.status_code}
                    return
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # Remove "data: " prefix
                        
                        if data == "[DONE]":
                            break
                        
                        try:
                            chunk = json.loads(data)
                            yield chunk
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            error_msg = f"Failed to create streaming chat completion: {e}"
            logger.error(error_msg)
            yield {"error": error_msg}
    
    async def create_completion_with_retry(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        **kwargs
    ) -> Dict[str, Any]:
        """Create completion with retry logic"""
        
        last_error = None
        
        for attempt in range(max_retries):
            try:
                result = await self.create_chat_completion(messages, model, **kwargs)
                
                # Check if successful
                if "error" not in result:
                    return result
                
                # If rate limited, wait longer
                if result.get("status_code") == 429:
                    wait_time = retry_delay * (2 ** attempt)
                    logger.warning(f"Rate limited, waiting {wait_time} seconds before retry {attempt + 1}")
                    await asyncio.sleep(wait_time)
                    continue
                
                last_error = result
                
            except Exception as e:
                last_error = {"error": str(e)}
                
                if attempt < max_retries - 1:
                    wait_time = retry_delay * (2 ** attempt)
                    logger.warning(f"Request failed, retrying in {wait_time} seconds: {e}")
                    await asyncio.sleep(wait_time)
        
        logger.error(f"All retry attempts failed: {last_error}")
        return last_error or {"error": "All retry attempts failed"}
    
    def create_conversation(self, conversation_id: str, model: Optional[str] = None) -> Conversation:
        """Create a new conversation"""
        conversation = Conversation(
            id=conversation_id,
            model=model or self.default_coding_model
        )
        self.conversations[conversation_id] = conversation
        return conversation
    
    def get_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Get an existing conversation"""
        return self.conversations.get(conversation_id)
    
    async def send_message(
        self,
        conversation_id: str,
        message: str,
        role: str = "user",
        model: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Send a message in a conversation"""
        
        # Get or create conversation
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            conversation = self.create_conversation(conversation_id, model)
        
        # Add user message
        conversation.add_message(role, message)
        
        # Get response
        messages = conversation.get_messages_for_api()
        result = await self.create_completion_with_retry(
            messages, 
            model or conversation.model,
            **kwargs
        )
        
        # Add assistant response if successful
        if "error" not in result and "choices" in result:
            assistant_message = result["choices"][0]["message"]["content"]
            conversation.add_message("assistant", assistant_message)
        
        return result
    
    def clear_conversation(self, conversation_id: str):
        """Clear a conversation"""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """Get conversation statistics"""
        return {
            "total_conversations": len(self.conversations),
            "rate_limiter": {
                "requests_per_minute": self.rate_limiter.requests_per_minute,
                "requests_per_day": self.rate_limiter.requests_per_day,
                "daily_count": self.rate_limiter.daily_count,
                "recent_requests": len(self.rate_limiter.request_times)
            }
        }

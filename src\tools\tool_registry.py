"""
Tool registry and execution engine for mock tool calling
"""
import json
import re
import asyncio
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import inspect

from src.config.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class ToolParameter:
    """Tool parameter definition"""
    name: str
    type: str  # string, integer, boolean, array, object
    description: str
    required: bool = True
    default: Any = None
    enum: Optional[List[Any]] = None


@dataclass
class ToolDefinition:
    """Tool definition for the registry"""
    name: str
    description: str
    parameters: List[ToolParameter]
    category: str
    enabled: bool = True
    dangerous: bool = False
    timeout_seconds: int = 30
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {
                    param.name: {
                        "type": param.type,
                        "description": param.description,
                        **({"enum": param.enum} if param.enum else {}),
                        **({"default": param.default} if param.default is not None else {})
                    }
                    for param in self.parameters
                },
                "required": [param.name for param in self.parameters if param.required]
            },
            "category": self.category,
            "enabled": self.enabled,
            "dangerous": self.dangerous
        }


@dataclass
class ToolCall:
    """Represents a tool call request"""
    name: str
    arguments: Dict[str, Any]
    call_id: Optional[str] = None


@dataclass
class ToolResult:
    """Result of tool execution"""
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


class BaseTool(ABC):
    """Base class for all tools"""
    
    def __init__(self):
        self.definition = self._create_definition()
    
    @abstractmethod
    def _create_definition(self) -> ToolDefinition:
        """Create tool definition"""
        pass
    
    @abstractmethod
    async def execute(self, arguments: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        """Execute the tool"""
        pass
    
    def validate_arguments(self, arguments: Dict[str, Any]) -> List[str]:
        """Validate tool arguments"""
        errors = []
        
        # Check required parameters
        for param in self.definition.parameters:
            if param.required and param.name not in arguments:
                errors.append(f"Missing required parameter: {param.name}")
        
        # Check parameter types and values
        for param_name, value in arguments.items():
            param = next((p for p in self.definition.parameters if p.name == param_name), None)
            if not param:
                errors.append(f"Unknown parameter: {param_name}")
                continue
            
            # Type validation
            if param.type == "string" and not isinstance(value, str):
                errors.append(f"Parameter '{param_name}' must be a string")
            elif param.type == "integer" and not isinstance(value, int):
                errors.append(f"Parameter '{param_name}' must be an integer")
            elif param.type == "boolean" and not isinstance(value, bool):
                errors.append(f"Parameter '{param_name}' must be a boolean")
            elif param.type == "array" and not isinstance(value, list):
                errors.append(f"Parameter '{param_name}' must be an array")
            elif param.type == "object" and not isinstance(value, dict):
                errors.append(f"Parameter '{param_name}' must be an object")
            
            # Enum validation
            if param.enum and value not in param.enum:
                errors.append(f"Parameter '{param_name}' must be one of: {param.enum}")
        
        return errors


class ToolRegistry:
    """Registry for managing and executing tools"""
    
    def __init__(self):
        self.tools: Dict[str, BaseTool] = {}
        self.execution_history: List[Dict[str, Any]] = []
        self._register_core_tools()
    
    def register_tool(self, tool: BaseTool):
        """Register a tool"""
        self.tools[tool.definition.name] = tool
        logger.info(f"Registered tool: {tool.definition.name}")
    
    def unregister_tool(self, tool_name: str):
        """Unregister a tool"""
        if tool_name in self.tools:
            del self.tools[tool_name]
            logger.info(f"Unregistered tool: {tool_name}")
    
    def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """Get a tool by name"""
        return self.tools.get(tool_name)
    
    def list_tools(self, category: Optional[str] = None, enabled_only: bool = True) -> List[ToolDefinition]:
        """List available tools"""
        tools = []
        for tool in self.tools.values():
            if enabled_only and not tool.definition.enabled:
                continue
            if category and tool.definition.category != category:
                continue
            tools.append(tool.definition)
        return tools
    
    def get_tools_schema(self) -> List[Dict[str, Any]]:
        """Get tools schema for AI models"""
        return [tool.definition.to_dict() for tool in self.tools.values() if tool.definition.enabled]
    
    async def execute_tool(
        self, 
        tool_call: ToolCall, 
        context: Dict[str, Any] = None,
        timeout: Optional[int] = None
    ) -> ToolResult:
        """Execute a tool call"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Get tool
            tool = self.get_tool(tool_call.name)
            if not tool:
                return ToolResult(
                    success=False,
                    error=f"Tool '{tool_call.name}' not found"
                )
            
            # Check if tool is enabled
            if not tool.definition.enabled:
                return ToolResult(
                    success=False,
                    error=f"Tool '{tool_call.name}' is disabled"
                )
            
            # Validate arguments
            validation_errors = tool.validate_arguments(tool_call.arguments)
            if validation_errors:
                return ToolResult(
                    success=False,
                    error=f"Validation errors: {', '.join(validation_errors)}"
                )
            
            # Execute with timeout
            execution_timeout = timeout or tool.definition.timeout_seconds
            
            try:
                result = await asyncio.wait_for(
                    tool.execute(tool_call.arguments, context or {}),
                    timeout=execution_timeout
                )
            except asyncio.TimeoutError:
                return ToolResult(
                    success=False,
                    error=f"Tool execution timed out after {execution_timeout} seconds"
                )
            
            # Record execution time
            execution_time = asyncio.get_event_loop().time() - start_time
            result.execution_time = execution_time
            
            # Log execution
            self._log_execution(tool_call, result, context)
            
            return result
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            error_result = ToolResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
            
            # Log failed execution
            self._log_execution(tool_call, error_result, context)
            
            return error_result
    
    async def execute_tool_calls(
        self, 
        tool_calls: List[ToolCall], 
        context: Dict[str, Any] = None,
        parallel: bool = False
    ) -> List[ToolResult]:
        """Execute multiple tool calls"""
        if parallel:
            # Execute in parallel
            tasks = [
                self.execute_tool(tool_call, context) 
                for tool_call in tool_calls
            ]
            return await asyncio.gather(*tasks, return_exceptions=False)
        else:
            # Execute sequentially
            results = []
            for tool_call in tool_calls:
                result = await self.execute_tool(tool_call, context)
                results.append(result)
                
                # Stop on first failure if needed
                if not result.success:
                    logger.warning(f"Tool call failed: {tool_call.name} - {result.error}")
            
            return results
    
    def _log_execution(self, tool_call: ToolCall, result: ToolResult, context: Dict[str, Any]):
        """Log tool execution"""
        log_entry = {
            "timestamp": asyncio.get_event_loop().time(),
            "tool_name": tool_call.name,
            "arguments": tool_call.arguments,
            "success": result.success,
            "execution_time": result.execution_time,
            "error": result.error,
            "context": context
        }
        
        self.execution_history.append(log_entry)
        
        # Keep only last 1000 executions
        if len(self.execution_history) > 1000:
            self.execution_history = self.execution_history[-1000:]
        
        # Log to file
        if result.success:
            logger.debug(f"Tool executed successfully: {tool_call.name}")
        else:
            logger.warning(f"Tool execution failed: {tool_call.name} - {result.error}")
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics"""
        if not self.execution_history:
            return {"total_executions": 0}
        
        total = len(self.execution_history)
        successful = sum(1 for entry in self.execution_history if entry["success"])
        failed = total - successful
        
        # Tool usage stats
        tool_usage = {}
        for entry in self.execution_history:
            tool_name = entry["tool_name"]
            if tool_name not in tool_usage:
                tool_usage[tool_name] = {"count": 0, "success": 0, "failed": 0}
            
            tool_usage[tool_name]["count"] += 1
            if entry["success"]:
                tool_usage[tool_name]["success"] += 1
            else:
                tool_usage[tool_name]["failed"] += 1
        
        # Average execution time
        avg_execution_time = sum(entry["execution_time"] for entry in self.execution_history) / total
        
        return {
            "total_executions": total,
            "successful_executions": successful,
            "failed_executions": failed,
            "success_rate": successful / total if total > 0 else 0,
            "average_execution_time": avg_execution_time,
            "tool_usage": tool_usage
        }
    
    def _register_core_tools(self):
        """Register core tools"""
        # Import and register core tools
        from src.tools.core_tools import (
            ReadFileTool, WriteFileTool, ListFilesTool, RunCommandTool,
            SearchCodebaseTool, AnalyzeErrorTool
        )
        
        core_tools = [
            ReadFileTool(),
            WriteFileTool(),
            ListFilesTool(),
            RunCommandTool(),
            SearchCodebaseTool(),
            AnalyzeErrorTool()
        ]
        
        for tool in core_tools:
            self.register_tool(tool)


# Global tool registry instance
tool_registry = ToolRegistry()

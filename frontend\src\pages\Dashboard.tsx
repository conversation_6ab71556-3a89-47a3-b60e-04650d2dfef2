import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { FiFolder, FiFile, FiCpu, FiActivity, FiTrendingUp } from 'react-icons/fi';
import { apiClient } from '../services/api';

export const Dashboard: React.FC = () => {
  const { data: status } = useQuery({
    queryKey: ['app-status'],
    queryFn: () => apiClient.getStatus()
  });
  const { data: projects } = useQuery({
    queryKey: ['projects'],
    queryFn: () => apiClient.getProjects()
  });
  const { data: agentStatus } = useQuery({
    queryKey: ['agent-status'],
    queryFn: () => apiClient.getAgentStatus()
  });

  const stats = [
    {
      name: 'Total Projects',
      value: projects?.length || 0,
      icon: FiFolder,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
    },
    {
      name: 'Active Agents',
      value: agentStatus?.active_runs || 0,
      icon: FiCpu,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
    },
    {
      name: 'Total Runs',
      value: agentStatus?.total_runs || 0,
      icon: FiActivity,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20',
    },
    {
      name: 'Success Rate',
      value: agentStatus?.total_runs && agentStatus.total_runs > 0
        ? `${Math.round((agentStatus.completed_runs / agentStatus.total_runs) * 100)}%`
        : '0%',
      icon: FiTrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">Welcome to EchoCode</h1>
        <p className="text-blue-100">
          Advanced AI coding assistant with intelligent codebase indexing and continuous improvement loops
        </p>
      </div>

      {/* Stats grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div
              key={stat.name}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center">
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.name}
                  </p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                    {stat.value}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* System status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Application info */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            System Information
          </h3>
          {status && (
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Version:</span>
                <span className="text-gray-900 dark:text-white font-medium">
                  {status.version}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Coding Model:</span>
                <span className="text-gray-900 dark:text-white font-medium">
                  {status.models?.coding}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Vision Model:</span>
                <span className="text-gray-900 dark:text-white font-medium">
                  {status.models?.vision}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Embedding Model:</span>
                <span className="text-gray-900 dark:text-white font-medium">
                  {status.models?.embedding}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Recent activity */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Recent Activity
          </h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="h-2 w-2 rounded-full bg-green-400"></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                System started successfully
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="h-2 w-2 rounded-full bg-blue-400"></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Database initialized
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="h-2 w-2 rounded-full bg-purple-400"></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                API endpoints ready
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick actions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <button className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
            <FiFolder className="h-4 w-4" />
            <span>Create Project</span>
          </button>
          <button className="flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
            <FiCpu className="h-4 w-4" />
            <span>Start Agent</span>
          </button>
          <button className="flex items-center justify-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
            <FiFile className="h-4 w-4" />
            <span>Browse Files</span>
          </button>
        </div>
      </div>
    </div>
  );
};

"""
Error log database model
"""
from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from pydantic import BaseModel

from src.config.database import Base


class ErrorLog(Base):
    """Error log database model"""
    __tablename__ = "error_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    agent_run_id = Column(Integer, ForeignKey("agent_runs.id"), nullable=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=True, index=True)
    error_type = Column(String(100), nullable=False, index=True)  # syntax, runtime, logical, api, system
    error_category = Column(String(100), nullable=False, index=True)  # compilation, execution, network, etc.
    error_message = Column(Text, nullable=False)
    stack_trace = Column(Text, nullable=True)
    context = Column(JSON, nullable=True)  # Additional context like file path, line number, etc.
    resolved = Column(String(50), default="unresolved", nullable=False)  # unresolved, resolved, ignored
    resolution_notes = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    resolved_at = Column(DateTime, nullable=True)
    
    # Relationships
    agent_run = relationship("AgentRun", back_populates="error_logs")
    project = relationship("Project")


class ErrorLogCreate(BaseModel):
    """Error log creation schema"""
    agent_run_id: Optional[int] = None
    project_id: Optional[int] = None
    error_type: str
    error_category: str
    error_message: str
    stack_trace: Optional[str] = None
    context: Optional[Dict[str, Any]] = None


class ErrorLogUpdate(BaseModel):
    """Error log update schema"""
    resolved: Optional[str] = None
    resolution_notes: Optional[str] = None
    resolved_at: Optional[datetime] = None


class ErrorLogResponse(BaseModel):
    """Error log response schema"""
    id: int
    agent_run_id: Optional[int] = None
    project_id: Optional[int] = None
    error_type: str
    error_category: str
    error_message: str
    stack_trace: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    resolved: str
    resolution_notes: Optional[str] = None
    created_at: datetime
    resolved_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

"""
Main indexing service that coordinates code parsing, dependency analysis, and symbol indexing
"""
from typing import Dict, List, Optional, Set, Any
from pathlib import Path
from sqlalchemy.orm import Session

from src.indexing.code_parser import CodeParser, LanguageType, SymbolInfo
from src.indexing.dependency_graph import DependencyGraphBuilder
from src.indexing.embedding_service import OllamaEmbeddingService, CodeChunker, HybridSearchService
from src.indexing.vector_store import VectorStore
from src.models.project import Project
from src.models.file import File
from src.config.logging_config import get_logger
from config.settings import settings

logger = get_logger(__name__)


class IndexingService:
    """Main service for codebase indexing and analysis"""

    def __init__(self, db: Session):
        self.db = db
        self.code_parser = CodeParser()
        self.dependency_graphs: Dict[int, DependencyGraphBuilder] = {}  # project_id -> graph
        self.embedding_service = OllamaEmbeddingService()
        self.vector_store = VectorStore()
        self.code_chunker = CodeChunker()
        self.hybrid_search = HybridSearchService(self.embedding_service)
    
    def index_project(self, project_id: int) -> Dict[str, Any]:
        """Index an entire project"""
        try:
            # Get project from database
            project = self.db.query(Project).filter(Project.id == project_id).first()
            if not project:
                raise ValueError(f"Project {project_id} not found")
            
            # Get all files in the project
            files = self.db.query(File).filter(File.project_id == project_id).all()
            
            # Create dependency graph builder
            graph_builder = DependencyGraphBuilder(self.code_parser)
            
            # Index each file
            indexed_files = 0
            failed_files = 0
            total_symbols = 0
            
            for file_record in files:
                try:
                    # Read file content
                    file_path = Path(project.path) / file_record.path
                    if not file_path.exists():
                        logger.warning(f"File not found: {file_path}")
                        failed_files += 1
                        continue
                    
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    # Add to dependency graph
                    if graph_builder.add_file(str(file_path), content):
                        indexed_files += 1
                        
                        # Count symbols
                        symbols = graph_builder.get_file_symbols(str(file_path))
                        total_symbols += len(symbols)
                    else:
                        failed_files += 1
                        
                except Exception as e:
                    logger.error(f"Failed to index file {file_record.path}: {e}")
                    failed_files += 1
            
            # Build dependencies
            graph_builder.build_dependencies()
            
            # Store the graph
            self.dependency_graphs[project_id] = graph_builder
            
            # Get statistics
            stats = graph_builder.get_statistics()
            
            result = {
                "project_id": project_id,
                "indexed_files": indexed_files,
                "failed_files": failed_files,
                "total_symbols": total_symbols,
                "statistics": stats,
                "circular_dependencies": graph_builder.find_circular_dependencies()
            }
            
            logger.info(f"Project indexing completed", **result)
            return result
            
        except Exception as e:
            logger.error(f"Failed to index project {project_id}: {e}")
            raise
    
    def index_file(self, project_id: int, file_path: str, content: str) -> Dict[str, Any]:
        """Index a single file"""
        try:
            # Get or create dependency graph for project
            if project_id not in self.dependency_graphs:
                self.dependency_graphs[project_id] = DependencyGraphBuilder(self.code_parser)
            
            graph_builder = self.dependency_graphs[project_id]
            
            # Add file to graph
            success = graph_builder.add_file(file_path, content)
            
            if success:
                # Rebuild dependencies for the project
                graph_builder.build_dependencies()
                
                # Get file symbols
                symbols = graph_builder.get_file_symbols(file_path)
                
                result = {
                    "file_path": file_path,
                    "success": True,
                    "symbols_found": len(symbols),
                    "language": graph_builder.file_nodes[file_path].language.value if file_path in graph_builder.file_nodes else None
                }
                
                logger.debug(f"File indexed successfully", **result)
                return result
            else:
                return {
                    "file_path": file_path,
                    "success": False,
                    "error": "Failed to parse file"
                }
                
        except Exception as e:
            logger.error(f"Failed to index file {file_path}: {e}")
            return {
                "file_path": file_path,
                "success": False,
                "error": str(e)
            }
    
    def get_file_dependencies(self, project_id: int, file_path: str) -> List[str]:
        """Get dependencies for a specific file"""
        if project_id not in self.dependency_graphs:
            return []
        
        graph_builder = self.dependency_graphs[project_id]
        return graph_builder.get_dependencies(file_path)
    
    def get_file_dependents(self, project_id: int, file_path: str) -> List[str]:
        """Get files that depend on the given file"""
        if project_id not in self.dependency_graphs:
            return []
        
        graph_builder = self.dependency_graphs[project_id]
        return graph_builder.get_dependents(file_path)
    
    def get_related_files(self, project_id: int, file_path: str, max_files: int = 10) -> List[Dict[str, Any]]:
        """Get files related to the given file"""
        if project_id not in self.dependency_graphs:
            return []
        
        graph_builder = self.dependency_graphs[project_id]
        related = graph_builder.get_related_files(file_path, max_files)
        
        return [
            {
                "file_path": path,
                "relevance_score": score,
                "relationship": self._determine_relationship(project_id, file_path, path)
            }
            for path, score in related
        ]
    
    def _determine_relationship(self, project_id: int, source_file: str, target_file: str) -> str:
        """Determine the relationship between two files"""
        if project_id not in self.dependency_graphs:
            return "unknown"
        
        graph_builder = self.dependency_graphs[project_id]
        
        if target_file in graph_builder.get_dependencies(source_file):
            return "dependency"
        elif target_file in graph_builder.get_dependents(source_file):
            return "dependent"
        else:
            return "related"
    
    def search_symbols(self, project_id: int, symbol_name: str) -> List[Dict[str, Any]]:
        """Search for symbols by name"""
        if project_id not in self.dependency_graphs:
            return []
        
        graph_builder = self.dependency_graphs[project_id]
        symbols = graph_builder.get_symbol_definitions(symbol_name)
        
        return [
            {
                "name": symbol.name,
                "type": symbol.type,
                "file_path": symbol.file_path,
                "start_line": symbol.start_line,
                "end_line": symbol.end_line,
                "scope": symbol.scope,
                "signature": symbol.signature,
                "docstring": symbol.docstring
            }
            for symbol in symbols
        ]
    
    def get_file_symbols(self, project_id: int, file_path: str) -> List[Dict[str, Any]]:
        """Get all symbols in a file"""
        if project_id not in self.dependency_graphs:
            return []
        
        graph_builder = self.dependency_graphs[project_id]
        symbols = graph_builder.get_file_symbols(file_path)
        
        return [
            {
                "name": symbol.name,
                "type": symbol.type,
                "start_line": symbol.start_line,
                "end_line": symbol.end_line,
                "scope": symbol.scope,
                "signature": symbol.signature,
                "docstring": symbol.docstring
            }
            for symbol in symbols
        ]
    
    def get_project_statistics(self, project_id: int) -> Dict[str, Any]:
        """Get indexing statistics for a project"""
        if project_id not in self.dependency_graphs:
            return {}
        
        graph_builder = self.dependency_graphs[project_id]
        return graph_builder.get_statistics()
    
    def export_dependency_graph(self, project_id: int, format: str = "json") -> Dict[str, Any]:
        """Export dependency graph for visualization"""
        if project_id not in self.dependency_graphs:
            return {}
        
        graph_builder = self.dependency_graphs[project_id]
        return graph_builder.export_graph(format)
    
    def find_circular_dependencies(self, project_id: int) -> List[List[str]]:
        """Find circular dependencies in the project"""
        if project_id not in self.dependency_graphs:
            return []
        
        graph_builder = self.dependency_graphs[project_id]
        return graph_builder.find_circular_dependencies()
    
    def get_file_importance_scores(self, project_id: int) -> Dict[str, float]:
        """Get importance scores for all files in the project"""
        if project_id not in self.dependency_graphs:
            return {}
        
        graph_builder = self.dependency_graphs[project_id]
        scores = {}
        
        for file_path in graph_builder.file_nodes.keys():
            scores[file_path] = graph_builder.get_file_importance_score(file_path)
        
        return scores
    
    def remove_file_from_index(self, project_id: int, file_path: str):
        """Remove a file from the index"""
        if project_id not in self.dependency_graphs:
            return
        
        graph_builder = self.dependency_graphs[project_id]
        
        # Remove from graph
        if file_path in graph_builder.graph:
            graph_builder.graph.remove_node(file_path)
        
        # Remove from file nodes
        if file_path in graph_builder.file_nodes:
            # Remove symbols from index
            file_node = graph_builder.file_nodes[file_path]
            for symbol in file_node.symbols:
                if symbol.name in graph_builder.symbol_index:
                    graph_builder.symbol_index[symbol.name] = [
                        s for s in graph_builder.symbol_index[symbol.name]
                        if s.file_path != file_path
                    ]
                    if not graph_builder.symbol_index[symbol.name]:
                        del graph_builder.symbol_index[symbol.name]
            
            del graph_builder.file_nodes[file_path]
        
        logger.debug(f"Removed file from index: {file_path}")
    
    async def index_file_embeddings(self, project_id: int, file_path: str, content: str) -> Dict[str, Any]:
        """Generate and store embeddings for a file"""
        try:
            # Get file symbols for better chunking
            symbols = []
            if project_id in self.dependency_graphs:
                symbols = self.get_file_symbols(project_id, file_path)

            # Chunk the code
            chunks = self.code_chunker.chunk_code(content, symbols, file_path)

            # Generate embeddings for chunks
            chunk_texts = [chunk["text"] for chunk in chunks]
            async with self.embedding_service:
                embedding_results = await self.embedding_service.generate_embeddings_batch(chunk_texts)

            # Prepare metadata for vector store
            valid_embeddings = []
            valid_metadata = []

            for chunk, embedding_result in zip(chunks, embedding_results):
                if embedding_result:
                    valid_embeddings.append(embedding_result)

                    metadata = {
                        "file_path": file_path,
                        "start_line": chunk.get("start_line", 0),
                        "end_line": chunk.get("end_line", 0),
                        "chunk_type": chunk.get("chunk_type", "unknown"),
                        "symbol_name": chunk.get("symbol_name", ""),
                        "symbol_type": chunk.get("symbol_type", ""),
                        "language": self._detect_language_string(file_path),
                        "chunk_id": chunk.get("chunk_id", 0)
                    }
                    valid_metadata.append(metadata)

            # Store in vector database
            if valid_embeddings:
                success = self.vector_store.add_embeddings(
                    project_id, valid_embeddings, valid_metadata
                )

                if success:
                    result = {
                        "file_path": file_path,
                        "chunks_processed": len(chunks),
                        "embeddings_generated": len(valid_embeddings),
                        "success": True
                    }
                    logger.info(f"Generated embeddings for file", **result)
                    return result

            return {
                "file_path": file_path,
                "chunks_processed": len(chunks),
                "embeddings_generated": 0,
                "success": False,
                "error": "No valid embeddings generated"
            }

        except Exception as e:
            logger.error(f"Failed to generate embeddings for file {file_path}: {e}")
            return {
                "file_path": file_path,
                "success": False,
                "error": str(e)
            }

    async def search_code_semantic(
        self,
        project_id: int,
        query: str,
        max_results: int = 10,
        file_filter: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Perform semantic search on code"""
        try:
            # Generate query embedding
            async with self.embedding_service:
                query_embedding_result = await self.embedding_service.generate_embedding(query)

            if not query_embedding_result:
                logger.warning("Failed to generate query embedding")
                return []

            # Prepare filter
            where_filter = {}
            if file_filter:
                where_filter["file_path"] = {"$regex": file_filter}

            # Search in vector store
            results = self.vector_store.search_similar(
                project_id,
                query_embedding_result.embedding,
                n_results=max_results,
                where_filter=where_filter if where_filter else None
            )

            # Enhance results with additional context
            enhanced_results = []
            for result in results:
                metadata = result["metadata"]
                enhanced_result = {
                    "text": result["text"],
                    "file_path": metadata.get("file_path", ""),
                    "start_line": metadata.get("start_line", 0),
                    "end_line": metadata.get("end_line", 0),
                    "similarity_score": result["similarity_score"],
                    "chunk_type": metadata.get("chunk_type", ""),
                    "symbol_name": metadata.get("symbol_name", ""),
                    "symbol_type": metadata.get("symbol_type", ""),
                    "language": metadata.get("language", "")
                }
                enhanced_results.append(enhanced_result)

            logger.debug(f"Semantic search returned {len(enhanced_results)} results")
            return enhanced_results

        except Exception as e:
            logger.error(f"Semantic search failed: {e}")
            return []

    async def search_code_hybrid(
        self,
        project_id: int,
        query: str,
        max_results: int = 10,
        semantic_weight: float = 0.7,
        text_weight: float = 0.3
    ) -> List[Dict[str, Any]]:
        """Perform hybrid search combining semantic and text similarity"""
        try:
            # Get all documents for the project (this could be optimized)
            # For now, we'll use semantic search and enhance with text similarity
            semantic_results = await self.search_code_semantic(project_id, query, max_results * 2)

            # Convert to format expected by hybrid search
            documents = []
            for result in semantic_results:
                doc = {
                    "text": result["text"],
                    "embedding": [],  # Would need to store embeddings in search results
                    "metadata": result
                }
                documents.append(doc)

            # Perform hybrid search
            hybrid_results = await self.hybrid_search.hybrid_search(
                query, documents, semantic_weight, text_weight
            )

            # Format results
            final_results = []
            for doc, score in hybrid_results[:max_results]:
                result = doc["metadata"].copy()
                result["hybrid_score"] = score
                final_results.append(result)

            return final_results

        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return []

    def _detect_language_string(self, file_path: str) -> str:
        """Detect language and return as string"""
        language = self.code_parser.detect_language(file_path)
        return language.value if language else "unknown"

    def clear_project_index(self, project_id: int):
        """Clear all indexing data for a project"""
        if project_id in self.dependency_graphs:
            del self.dependency_graphs[project_id]

        # Clear vector embeddings
        self.vector_store.clear_project_embeddings(project_id)

        logger.info(f"Cleared index for project {project_id}")

    def is_project_indexed(self, project_id: int) -> bool:
        """Check if a project is indexed"""
        return project_id in self.dependency_graphs

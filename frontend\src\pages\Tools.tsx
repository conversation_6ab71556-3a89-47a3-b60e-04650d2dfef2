import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { FiTool, FiPlay, FiCode, FiFileText, FiTerminal, FiSearch, FiAlertCircle } from 'react-icons/fi';
import { apiClient } from '../services/api';

export const Tools: React.FC = () => {
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  const [toolArguments, setToolArguments] = useState<Record<string, any>>({});
  const [executionResult, setExecutionResult] = useState<any>(null);

  const { data: tools, isLoading } = useQuery({
    queryKey: ['tools'],
    queryFn: apiClient.getTools
  });
  const { data: categories } = useQuery({
    queryKey: ['tool-categories'],
    queryFn: apiClient.getToolCategories
  });

  const executeToolMutation = useMutation(
    ({ toolName, arguments: args }: { toolName: string; arguments: Record<string, any> }) =>
      apiClient.executeTool(toolName, args),
    {
      onSuccess: (result: any) => {
        setExecutionResult(result);
      },
    }
  );

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'file_operations':
        return FiFileText;
      case 'system':
        return FiTerminal;
      case 'search':
        return FiSearch;
      case 'analysis':
        return FiAlertCircle;
      default:
        return FiTool;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'file_operations':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
      case 'system':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'search':
        return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20';
      case 'analysis':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  const handleExecuteTool = () => {
    if (selectedTool) {
      executeToolMutation.mutate({
        toolName: selectedTool,
        arguments: toolArguments,
      });
    }
  };

  const handleArgumentChange = (paramName: string, value: any) => {
    setToolArguments(prev => ({
      ...prev,
      [paramName]: value,
    }));
  };

  const selectedToolData = tools?.find((tool: any) => tool.name === selectedTool);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Tools</h1>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {tools?.length || 0} tools available
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tools list */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Available Tools</h3>
          </div>
          
          <div className="divide-y divide-gray-200 dark:divide-gray-700 max-h-96 overflow-y-auto">
            {tools?.map((tool: any) => {
              const Icon = getCategoryIcon(tool.category);
              const isSelected = selectedTool === tool.name;
              
              return (
                <div
                  key={tool.name}
                  className={`px-6 py-4 cursor-pointer transition-colors ${
                    isSelected
                      ? 'bg-blue-50 dark:bg-blue-900/20'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                  onClick={() => {
                    setSelectedTool(tool.name);
                    setToolArguments({});
                    setExecutionResult(null);
                  }}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${getCategoryColor(tool.category)}`}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {tool.name}
                        </h4>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                          tool.enabled
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                            : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                        }`}>
                          {tool.enabled ? 'Enabled' : 'Disabled'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        {tool.description}
                      </p>
                      <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                        Category: {tool.category}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Tool execution panel */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {selectedToolData ? `Execute: ${selectedToolData.name}` : 'Select a Tool'}
            </h3>
          </div>
          
          <div className="p-6">
            {selectedToolData ? (
              <div className="space-y-4">
                {/* Tool description */}
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {selectedToolData.description}
                  </p>
                </div>

                {/* Parameters */}
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">Parameters</h4>
                  {Object.entries(selectedToolData.parameters).map(([paramName, paramInfo]: [string, any]) => (
                    <div key={paramName}>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {paramName}
                        {!paramInfo.optional && <span className="text-red-500 ml-1">*</span>}
                      </label>
                      <input
                        type={paramInfo.type === 'integer' ? 'number' : 'text'}
                        value={toolArguments[paramName] || ''}
                        onChange={(e) => handleArgumentChange(paramName, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        placeholder={paramInfo.description}
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {paramInfo.description}
                      </p>
                    </div>
                  ))}
                </div>

                {/* Execute button */}
                <button
                  onClick={handleExecuteTool}
                  disabled={executeToolMutation.isLoading || !selectedToolData.enabled}
                  className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <FiPlay className="h-4 w-4" />
                  <span>
                    {executeToolMutation.isLoading ? 'Executing...' : 'Execute Tool'}
                  </span>
                </button>

                {/* Execution result */}
                {executionResult && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                      Execution Result
                    </h4>
                    <div className={`p-3 rounded-lg ${
                      executionResult.success
                        ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                        : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
                    }`}>
                      <div className="flex items-start space-x-2">
                        <div className={`mt-0.5 ${
                          executionResult.success ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {executionResult.success ? '✓' : '✗'}
                        </div>
                        <div className="flex-1">
                          {executionResult.success ? (
                            <div>
                              <p className="text-sm text-green-800 dark:text-green-400 font-medium">
                                Success
                              </p>
                              <pre className="text-xs text-green-700 dark:text-green-300 mt-1 whitespace-pre-wrap">
                                {JSON.stringify(executionResult.result, null, 2)}
                              </pre>
                            </div>
                          ) : (
                            <div>
                              <p className="text-sm text-red-800 dark:text-red-400 font-medium">
                                Error
                              </p>
                              <p className="text-xs text-red-700 dark:text-red-300 mt-1">
                                {executionResult.error}
                              </p>
                            </div>
                          )}
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                            Execution time: {executionResult.execution_time.toFixed(3)}s
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiTool className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                  Select a tool to execute
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Choose a tool from the list to see its parameters and execute it.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Tool categories overview */}
      {categories && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Tool Categories</h3>
          <div className="flex flex-wrap gap-2">
            {categories.categories.map((category: any) => {
              const Icon = getCategoryIcon(category);
              const toolCount = tools?.filter((tool: any) => tool.category === category).length || 0;
              
              return (
                <div
                  key={category}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${getCategoryColor(category)}`}
                >
                  <Icon className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    {category.replace('_', ' ')} ({toolCount})
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

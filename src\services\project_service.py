"""
Project service for managing projects
"""
import os
from pathlib import Path
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func

from src.models.project import Project, ProjectCreate, ProjectUpdate
from src.models.file import File
from src.config.logging_config import get_logger
from config.settings import settings

logger = get_logger(__name__)


class ProjectService:
    """Service for managing projects"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def list_projects(self, skip: int = 0, limit: int = 100) -> List[Project]:
        """List all projects with file counts"""
        projects = (
            self.db.query(Project)
            .offset(skip)
            .limit(limit)
            .all()
        )
        
        # Add file counts
        for project in projects:
            file_count = self.db.query(func.count(File.id)).filter(File.project_id == project.id).scalar()
            project.file_count = file_count or 0
        
        return projects
    
    def get_project(self, project_id: int) -> Optional[Project]:
        """Get project by ID"""
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if project:
            file_count = self.db.query(func.count(File.id)).filter(File.project_id == project.id).scalar()
            project.file_count = file_count or 0
        return project
    
    def create_project(self, project_data: ProjectCreate) -> Project:
        """Create a new project"""
        # Validate project path
        project_path = Path(project_data.path)
        if not project_path.exists():
            raise ValueError(f"Project path does not exist: {project_data.path}")
        
        if not project_path.is_dir():
            raise ValueError(f"Project path is not a directory: {project_data.path}")
        
        # Check if project already exists
        existing = self.db.query(Project).filter(Project.path == project_data.path).first()
        if existing:
            raise ValueError(f"Project already exists at path: {project_data.path}")
        
        # Create project
        project = Project(
            name=project_data.name,
            path=str(project_path.absolute()),
            description=project_data.description
        )
        
        self.db.add(project)
        self.db.commit()
        self.db.refresh(project)
        
        project.file_count = 0
        logger.info("Project created", project_id=project.id, name=project.name, path=project.path)
        
        return project
    
    def update_project(self, project_id: int, project_data: ProjectUpdate) -> Optional[Project]:
        """Update project"""
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project:
            return None
        
        # Update fields
        if project_data.name is not None:
            project.name = project_data.name
        if project_data.description is not None:
            project.description = project_data.description
        if project_data.is_active is not None:
            project.is_active = project_data.is_active
        
        self.db.commit()
        self.db.refresh(project)
        
        # Add file count
        file_count = self.db.query(func.count(File.id)).filter(File.project_id == project.id).scalar()
        project.file_count = file_count or 0
        
        return project
    
    def delete_project(self, project_id: int) -> bool:
        """Delete project"""
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project:
            return False
        
        self.db.delete(project)
        self.db.commit()
        
        logger.info("Project deleted", project_id=project_id)
        return True
    
    async def scan_project(self, project_id: int) -> Dict[str, Any]:
        """Scan project directory for files and update database"""
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project:
            raise ValueError("Project not found")
        
        project_path = Path(project.path)
        if not project_path.exists():
            raise ValueError(f"Project path does not exist: {project.path}")
        
        files_found = 0
        files_added = 0
        files_updated = 0
        
        # Get supported extensions
        supported_extensions = set(settings.supported_extensions)
        
        # Scan directory recursively
        for file_path in project_path.rglob("*"):
            if file_path.is_file():
                # Check if file extension is supported
                if file_path.suffix.lower() not in supported_extensions:
                    continue
                
                # Check file size
                try:
                    file_size = file_path.stat().st_size
                    if file_size > settings.max_file_size_mb * 1024 * 1024:
                        logger.warning("File too large, skipping", file_path=str(file_path), size_mb=file_size / (1024 * 1024))
                        continue
                except OSError:
                    logger.warning("Cannot access file, skipping", file_path=str(file_path))
                    continue
                
                files_found += 1
                
                # Calculate relative path
                relative_path = file_path.relative_to(project_path)
                
                # Calculate content hash
                try:
                    import hashlib
                    with open(file_path, 'rb') as f:
                        content_hash = hashlib.sha256(f.read()).hexdigest()
                except Exception as e:
                    logger.warning("Cannot read file for hashing, skipping", file_path=str(file_path), error=str(e))
                    continue
                
                # Check if file exists in database
                existing_file = (
                    self.db.query(File)
                    .filter(File.project_id == project_id, File.path == str(relative_path))
                    .first()
                )
                
                if existing_file:
                    # Update if hash changed
                    if existing_file.content_hash != content_hash:
                        existing_file.size = file_size
                        existing_file.content_hash = content_hash
                        files_updated += 1
                else:
                    # Add new file
                    new_file = File(
                        project_id=project_id,
                        path=str(relative_path),
                        name=file_path.name,
                        extension=file_path.suffix.lower(),
                        size=file_size,
                        content_hash=content_hash
                    )
                    self.db.add(new_file)
                    files_added += 1
        
        # Commit changes
        self.db.commit()
        
        result = {
            "files_found": files_found,
            "files_added": files_added,
            "files_updated": files_updated
        }
        
        logger.info("Project scan completed", project_id=project_id, **result)
        return result

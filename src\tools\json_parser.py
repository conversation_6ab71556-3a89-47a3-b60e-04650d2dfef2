"""
JSON response parser for mock tool calling
"""
import json
import re
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

from src.tools.tool_registry import ToolCall
from src.config.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class ParsedResponse:
    """Parsed AI response containing tool calls and text"""
    tool_calls: List[ToolCall]
    text_content: str
    raw_response: str
    parsing_errors: List[str]
    confidence: float  # 0.0 to 1.0


class JSONToolCallParser:
    """Parser for extracting tool calls from AI responses"""
    
    def __init__(self):
        # Patterns for finding JSON tool calls
        self.json_patterns = [
            # Standard JSON format
            r'```json\s*(\{.*?\})\s*```',
            r'```\s*(\{.*?\})\s*```',
            # Tool call format
            r'<tool_call>\s*(\{.*?\})\s*</tool_call>',
            r'TOOL_CALL:\s*(\{.*?\})',
            # Direct JSON objects
            r'(\{[^{}]*"tool"[^{}]*\})',
            r'(\{[^{}]*"function"[^{}]*\})',
            # Array format
            r'\[(\{.*?\})\]',
        ]
        
        # Expected tool call schemas
        self.expected_schemas = [
            # Schema 1: Direct tool format
            {"tool", "arguments"},
            {"tool", "args"},
            {"name", "arguments"},
            {"name", "args"},
            # Schema 2: Function call format
            {"function", "arguments"},
            {"function", "args"},
            # Schema 3: Action format
            {"action", "parameters"},
            {"action", "params"},
        ]
    
    def parse_response(self, response: str) -> ParsedResponse:
        """Parse AI response for tool calls"""
        tool_calls = []
        parsing_errors = []
        
        # Extract JSON objects from response
        json_objects = self._extract_json_objects(response)
        
        # Parse each JSON object
        for json_obj in json_objects:
            try:
                tool_call = self._parse_json_object(json_obj)
                if tool_call:
                    tool_calls.append(tool_call)
            except Exception as e:
                parsing_errors.append(f"Failed to parse JSON object: {str(e)}")
        
        # Extract text content (remove JSON blocks)
        text_content = self._extract_text_content(response, json_objects)
        
        # Calculate confidence based on parsing success
        confidence = self._calculate_confidence(tool_calls, parsing_errors, response)
        
        return ParsedResponse(
            tool_calls=tool_calls,
            text_content=text_content,
            raw_response=response,
            parsing_errors=parsing_errors,
            confidence=confidence
        )
    
    def _extract_json_objects(self, response: str) -> List[Dict[str, Any]]:
        """Extract JSON objects from response text"""
        json_objects = []
        
        # Try each pattern
        for pattern in self.json_patterns:
            matches = re.findall(pattern, response, re.DOTALL | re.IGNORECASE)
            
            for match in matches:
                try:
                    # Clean up the JSON string
                    json_str = self._clean_json_string(match)
                    
                    # Parse JSON
                    json_obj = json.loads(json_str)
                    
                    # Validate it looks like a tool call
                    if self._is_tool_call_object(json_obj):
                        json_objects.append(json_obj)
                        
                except json.JSONDecodeError as e:
                    logger.debug(f"Failed to parse JSON: {match[:100]}... Error: {e}")
                    continue
                except Exception as e:
                    logger.debug(f"Error processing JSON match: {e}")
                    continue
        
        # Try to find JSON objects without delimiters
        json_objects.extend(self._find_bare_json_objects(response))
        
        return json_objects
    
    def _clean_json_string(self, json_str: str) -> str:
        """Clean up JSON string for parsing"""
        # Remove common formatting issues
        json_str = json_str.strip()
        
        # Fix common JSON issues
        # Replace single quotes with double quotes (but not in strings)
        json_str = re.sub(r"(?<!\\)'([^']*)'", r'"\1"', json_str)
        
        # Fix trailing commas
        json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
        
        # Fix missing quotes around keys
        json_str = re.sub(r'(\w+):', r'"\1":', json_str)
        
        return json_str
    
    def _find_bare_json_objects(self, response: str) -> List[Dict[str, Any]]:
        """Find JSON objects without delimiters"""
        json_objects = []
        
        # Look for patterns that suggest JSON objects
        lines = response.split('\n')
        current_json = []
        brace_count = 0
        in_json = False
        
        for line in lines:
            stripped = line.strip()
            
            # Start of JSON object
            if stripped.startswith('{') and any(key in stripped.lower() for key in ['tool', 'function', 'action', 'name']):
                in_json = True
                current_json = [line]
                brace_count = stripped.count('{') - stripped.count('}')
            elif in_json:
                current_json.append(line)
                brace_count += stripped.count('{') - stripped.count('}')
                
                # End of JSON object
                if brace_count <= 0:
                    json_str = '\n'.join(current_json)
                    try:
                        json_str = self._clean_json_string(json_str)
                        json_obj = json.loads(json_str)
                        if self._is_tool_call_object(json_obj):
                            json_objects.append(json_obj)
                    except:
                        pass
                    
                    in_json = False
                    current_json = []
                    brace_count = 0
        
        return json_objects
    
    def _is_tool_call_object(self, obj: Dict[str, Any]) -> bool:
        """Check if object looks like a tool call"""
        if not isinstance(obj, dict):
            return False
        
        obj_keys = set(obj.keys())
        
        # Check against expected schemas
        for schema in self.expected_schemas:
            if schema.issubset(obj_keys):
                return True
        
        # Additional heuristics
        if any(key in obj_keys for key in ['tool', 'function', 'action', 'command']):
            return True
        
        return False
    
    def _parse_json_object(self, json_obj: Dict[str, Any]) -> Optional[ToolCall]:
        """Parse JSON object into ToolCall"""
        try:
            # Extract tool name
            tool_name = None
            arguments = {}
            
            # Try different naming conventions
            if 'tool' in json_obj:
                tool_name = json_obj['tool']
                arguments = json_obj.get('arguments', json_obj.get('args', {}))
            elif 'name' in json_obj:
                tool_name = json_obj['name']
                arguments = json_obj.get('arguments', json_obj.get('args', {}))
            elif 'function' in json_obj:
                if isinstance(json_obj['function'], str):
                    tool_name = json_obj['function']
                    arguments = json_obj.get('arguments', json_obj.get('args', {}))
                elif isinstance(json_obj['function'], dict):
                    tool_name = json_obj['function'].get('name')
                    arguments = json_obj['function'].get('arguments', json_obj['function'].get('args', {}))
            elif 'action' in json_obj:
                tool_name = json_obj['action']
                arguments = json_obj.get('parameters', json_obj.get('params', {}))
            
            if not tool_name:
                return None
            
            # Ensure arguments is a dict
            if not isinstance(arguments, dict):
                arguments = {}
            
            return ToolCall(
                name=tool_name,
                arguments=arguments,
                call_id=json_obj.get('id', json_obj.get('call_id'))
            )
            
        except Exception as e:
            logger.error(f"Failed to parse tool call object: {e}")
            return None
    
    def _extract_text_content(self, response: str, json_objects: List[Dict[str, Any]]) -> str:
        """Extract text content excluding JSON blocks"""
        text_content = response
        
        # Remove JSON code blocks
        for pattern in self.json_patterns:
            text_content = re.sub(pattern, '', text_content, flags=re.DOTALL | re.IGNORECASE)
        
        # Clean up whitespace
        text_content = re.sub(r'\n\s*\n', '\n\n', text_content)
        text_content = text_content.strip()
        
        return text_content
    
    def _calculate_confidence(
        self, 
        tool_calls: List[ToolCall], 
        parsing_errors: List[str], 
        response: str
    ) -> float:
        """Calculate confidence in parsing results"""
        confidence = 1.0
        
        # Reduce confidence for parsing errors
        if parsing_errors:
            confidence -= 0.2 * len(parsing_errors)
        
        # Increase confidence for successful tool calls
        if tool_calls:
            confidence += 0.1 * len(tool_calls)
        
        # Check for clear tool call indicators
        tool_indicators = ['tool_call', 'function_call', 'action', 'execute']
        indicator_count = sum(1 for indicator in tool_indicators if indicator in response.lower())
        confidence += 0.1 * indicator_count
        
        # Ensure confidence is between 0 and 1
        return max(0.0, min(1.0, confidence))
    
    def create_tool_call_prompt(self, available_tools: List[Dict[str, Any]]) -> str:
        """Create a prompt template for tool calling"""
        tools_description = []
        
        for tool in available_tools:
            tool_desc = f"""
Tool: {tool['name']}
Description: {tool['description']}
Parameters: {json.dumps(tool['parameters'], indent=2)}
"""
            tools_description.append(tool_desc)
        
        prompt = f"""
You have access to the following tools. To use a tool, respond with a JSON object in this format:

```json
{{
    "tool": "tool_name",
    "arguments": {{
        "parameter1": "value1",
        "parameter2": "value2"
    }}
}}
```

Available tools:
{''.join(tools_description)}

You can use multiple tools by providing multiple JSON objects. Always explain your reasoning before using tools.
"""
        
        return prompt.strip()
    
    def validate_tool_call_response(self, response: str, available_tools: List[str]) -> Dict[str, Any]:
        """Validate a tool call response"""
        parsed = self.parse_response(response)
        
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "tool_calls_found": len(parsed.tool_calls),
            "confidence": parsed.confidence
        }
        
        # Check if tool calls are valid
        for tool_call in parsed.tool_calls:
            if tool_call.name not in available_tools:
                validation_results["valid"] = False
                validation_results["errors"].append(f"Unknown tool: {tool_call.name}")
        
        # Check for parsing errors
        if parsed.parsing_errors:
            validation_results["warnings"].extend(parsed.parsing_errors)
        
        # Check confidence
        if parsed.confidence < 0.5:
            validation_results["warnings"].append("Low confidence in parsing results")
        
        return validation_results

"""
Files API endpoints
"""
from typing import List, Optional
from pathlib import Path

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from pydantic import BaseModel

from src.config.database import get_database
from src.services.file_service import FileService
from src.config.logging_config import get_logger

logger = get_logger(__name__)
router = APIRouter()


class FileResponse(BaseModel):
    id: int
    project_id: int
    path: str
    name: str
    extension: str
    size: int
    content_hash: str
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True


class FileContentResponse(BaseModel):
    content: str
    encoding: str = "utf-8"


class FileUpdateRequest(BaseModel):
    content: str


@router.get("/project/{project_id}", response_model=List[FileResponse])
async def list_project_files(
    project_id: int,
    skip: int = 0,
    limit: int = 1000,
    extension: Optional[str] = None,
    db: Session = Depends(get_database)
):
    """List files in a project"""
    try:
        service = FileService(db)
        files = service.list_project_files(
            project_id=project_id,
            skip=skip,
            limit=limit,
            extension=extension
        )
        return files
    except Exception as e:
        logger.error("Failed to list project files", project_id=project_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list project files"
        )


@router.get("/{file_id}", response_model=FileResponse)
async def get_file(
    file_id: int,
    db: Session = Depends(get_database)
):
    """Get file metadata by ID"""
    try:
        service = FileService(db)
        file = service.get_file(file_id)
        if not file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        return file
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get file", file_id=file_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get file"
        )


@router.get("/{file_id}/content", response_model=FileContentResponse)
async def get_file_content(
    file_id: int,
    db: Session = Depends(get_database)
):
    """Get file content"""
    try:
        service = FileService(db)
        content = service.get_file_content(file_id)
        if content is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found or cannot be read"
            )
        return FileContentResponse(content=content)
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get file content", file_id=file_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get file content"
        )


@router.put("/{file_id}/content")
async def update_file_content(
    file_id: int,
    request: FileUpdateRequest,
    db: Session = Depends(get_database)
):
    """Update file content"""
    try:
        service = FileService(db)
        success = service.update_file_content(file_id, request.content)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found or cannot be updated"
            )
        logger.info("File content updated", file_id=file_id)
        return {"message": "File content updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update file content", file_id=file_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update file content"
        )


@router.delete("/{file_id}")
async def delete_file(
    file_id: int,
    db: Session = Depends(get_database)
):
    """Delete file"""
    try:
        service = FileService(db)
        success = service.delete_file(file_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        logger.info("File deleted", file_id=file_id)
        return {"message": "File deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete file", file_id=file_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete file"
        )


@router.post("/search")
async def search_files(
    query: str,
    project_id: Optional[int] = None,
    file_types: Optional[List[str]] = None,
    limit: int = 50,
    db: Session = Depends(get_database)
):
    """Search files by content or metadata"""
    try:
        service = FileService(db)
        results = await service.search_files(
            query=query,
            project_id=project_id,
            file_types=file_types,
            limit=limit
        )
        return {"results": results, "query": query}
    except Exception as e:
        logger.error("Failed to search files", query=query, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search files"
        )

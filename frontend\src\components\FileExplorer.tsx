import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { ScrollArea } from './ui/scroll-area';
import { 
  FileText, 
  Folder, 
  Search, 
  Filter,
  Eye,
  Edit,
  Trash2,
  Download
} from 'lucide-react';

interface FileItem {
  id: number;
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  extension?: string;
  language?: string;
  modified_at: string;
}

interface FileExplorerProps {
  projectId: number;
}

export const FileExplorer: React.FC<FileExplorerProps> = ({ projectId }) => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [filteredFiles, setFilteredFiles] = useState<FileItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [fileContent, setFileContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [filterExtension, setFilterExtension] = useState<string>('');

  useEffect(() => {
    fetchFiles();
  }, [projectId]);

  useEffect(() => {
    filterFiles();
  }, [files, searchQuery, filterExtension]);

  const fetchFiles = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/files/project/${projectId}`);
      if (response.ok) {
        const data = await response.json();
        setFiles(data);
      }
    } catch (error) {
      console.error('Failed to fetch files:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterFiles = () => {
    let filtered = files;

    if (searchQuery) {
      filtered = filtered.filter(file => 
        file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        file.path.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (filterExtension) {
      filtered = filtered.filter(file => file.extension === filterExtension);
    }

    setFilteredFiles(filtered);
  };

  const fetchFileContent = async (fileId: number) => {
    try {
      const response = await fetch(`/api/files/${fileId}/content`);
      if (response.ok) {
        const data = await response.json();
        setFileContent(data.content);
      }
    } catch (error) {
      console.error('Failed to fetch file content:', error);
    }
  };

  const getFileIcon = (file: FileItem) => {
    return file.type === 'directory' ? (
      <Folder className="h-4 w-4 text-blue-500" />
    ) : (
      <FileText className="h-4 w-4 text-gray-500" />
    );
  };

  const getLanguageColor = (language: string) => {
    const colors: Record<string, string> = {
      javascript: 'bg-yellow-500',
      typescript: 'bg-blue-500',
      python: 'bg-green-500',
      java: 'bg-red-500',
      cpp: 'bg-purple-500',
      rust: 'bg-orange-500',
      go: 'bg-cyan-500',
    };
    return colors[language.toLowerCase()] || 'bg-gray-500';
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const uniqueExtensions = Array.from(new Set(files.map(f => f.extension).filter(Boolean)));

  return (
    <div className="space-y-6">
      {/* Search and Filter */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            File Explorer
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search files..."
                value={searchQuery}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            <select
              className="px-3 py-2 border rounded"
              value={filterExtension}
              onChange={(e) => setFilterExtension(e.target.value)}
            >
              <option value="">All Extensions</option>
              {uniqueExtensions.map(ext => (
                <option key={ext} value={ext}>{ext}</option>
              ))}
            </select>
            <Button onClick={fetchFiles}>
              <Filter className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* File List */}
        <Card>
          <CardHeader>
            <CardTitle>Files ({filteredFiles.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-96">
              {isLoading ? (
                <div className="text-center py-8">Loading files...</div>
              ) : filteredFiles.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No files found
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredFiles.map((file) => (
                    <div
                      key={file.id}
                      className={`p-3 border rounded cursor-pointer transition-colors ${
                        selectedFile === file.path ? 'bg-accent' : 'hover:bg-accent/50'
                      }`}
                      onClick={() => {
                        setSelectedFile(file.path);
                        if (file.type === 'file') {
                          fetchFileContent(file.id);
                        }
                      }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        {getFileIcon(file)}
                        <span className="font-medium">{file.name}</span>
                        {file.language && (
                          <Badge className={`text-white ${getLanguageColor(file.language)}`}>
                            {file.language}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="text-sm text-muted-foreground">
                        {file.path}
                      </div>
                      
                      <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                        <span>{formatFileSize(file.size)}</span>
                        <span>{new Date(file.modified_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>

        {/* File Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>File Content</span>
              {selectedFile && (
                <div className="flex gap-2">
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedFile ? (
              <ScrollArea className="h-96">
                <pre className="text-sm bg-gray-50 dark:bg-gray-900 p-4 rounded overflow-x-auto">
                  <code>{fileContent || 'Loading content...'}</code>
                </pre>
              </ScrollArea>
            ) : (
              <div className="text-center text-muted-foreground py-8">
                Select a file to view its content
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

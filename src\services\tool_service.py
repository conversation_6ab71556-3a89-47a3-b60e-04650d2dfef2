"""
Tool execution service for AI agents
"""
import asyncio
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from src.tools.tool_registry import tool_registry, ToolCall, ToolResult
from src.tools.json_parser import <PERSON><PERSON><PERSON><PERSON><PERSON>allPars<PERSON>, ParsedResponse
from src.services.openrouter_client import OpenRouterClient
from src.indexing.indexing_service import IndexingService
from src.config.logging_config import get_logger

logger = get_logger(__name__)


class ToolExecutionService:
    """Service for executing tools through AI responses"""

    def __init__(self, db: Session):
        self.db = db
        self.tool_registry = tool_registry
        self.json_parser = JSONToolCallParser()
        self.indexing_service = IndexingService(db)
        self.execution_history: List[Dict[str, Any]] = []

    async def execute_ai_response(
        self,
        ai_response: str,
        context: Dict[str, Any],
        max_iterations: int = 5,
        auto_approve_safe: bool = True
    ) -> Dict[str, Any]:
        """Execute tools from AI response with iterative refinement"""

        execution_log = {
            "original_response": ai_response,
            "iterations": [],
            "final_results": [],
            "success": True,
            "context": context
        }

        current_response = ai_response
        iteration = 0

        while iteration < max_iterations:
            iteration += 1

            # Parse the response for tool calls
            parsed = self.json_parser.parse_response(current_response)

            iteration_log = {
                "iteration": iteration,
                "parsed_response": {
                    "tool_calls_found": len(parsed.tool_calls),
                    "text_content": parsed.text_content,
                    "confidence": parsed.confidence,
                    "parsing_errors": parsed.parsing_errors
                },
                "tool_executions": [],
                "follow_up_needed": False
            }

            # Execute tool calls
            if parsed.tool_calls:
                for tool_call in parsed.tool_calls:
                    # Check if tool execution should be approved
                    if not auto_approve_safe and self._is_dangerous_tool(tool_call.name):
                        execution_result = ToolResult(
                            success=False,
                            error="Tool execution requires manual approval (dangerous operation)"
                        )
                    else:
                        # Execute the tool
                        execution_result = await self.tool_registry.execute_tool(
                            tool_call, context
                        )

                    tool_execution_log = {
                        "tool_name": tool_call.name,
                        "arguments": tool_call.arguments,
                        "success": execution_result.success,
                        "result": execution_result.result,
                        "error": execution_result.error,
                        "execution_time": execution_result.execution_time
                    }

                    iteration_log["tool_executions"].append(tool_execution_log)

                    # Check if we need follow-up
                    if not execution_result.success:
                        iteration_log["follow_up_needed"] = True

            execution_log["iterations"].append(iteration_log)

            # If no tool calls found or all successful, we're done
            if not parsed.tool_calls or not iteration_log["follow_up_needed"]:
                break

            # Generate follow-up response for failed tools
            if iteration_log["follow_up_needed"] and iteration < max_iterations:
                current_response = await self._generate_follow_up_response(
                    iteration_log, context
                )

        # Compile final results
        execution_log["final_results"] = self._compile_final_results(execution_log)
        execution_log["success"] = all(
            result.get("success", False) for result in execution_log["final_results"]
        )

        # Store in history
        self.execution_history.append(execution_log)

        return execution_log

    async def execute_tool_directly(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        context: Dict[str, Any]
    ) -> ToolResult:
        """Execute a single tool directly"""
        tool_call = ToolCall(name=tool_name, arguments=arguments)
        return await self.tool_registry.execute_tool(tool_call, context)

    async def get_ai_tool_response(
        self,
        prompt: str,
        context: Dict[str, Any],
        model: Optional[str] = None,
        include_tools: bool = True
    ) -> Dict[str, Any]:
        """Get AI response with tool calling capability"""

        # Prepare context for AI
        enhanced_context = await self._prepare_ai_context(context)

        # Get available tools
        available_tools = self.tool_registry.get_tools_schema() if include_tools else []

        # Create tool calling prompt
        if include_tools and available_tools:
            tool_prompt = self.json_parser.create_tool_call_prompt(available_tools)
            full_prompt = f"{tool_prompt}\n\nUser Request: {prompt}\n\nContext: {enhanced_context}"
        else:
            full_prompt = f"{prompt}\n\nContext: {enhanced_context}"

        # Get AI response
        async with OpenRouterClient() as client:
            messages = [
                {"role": "system", "content": "You are an AI coding assistant with access to tools. Use tools when needed to complete tasks."},
                {"role": "user", "content": full_prompt}
            ]

            response = await client.create_completion_with_retry(
                messages, model, temperature=0.3
            )

        if "error" in response:
            return {
                "success": False,
                "error": response["error"],
                "ai_response": None
            }

        ai_response = response["choices"][0]["message"]["content"]

        # Execute tools if found
        execution_result = await self.execute_ai_response(ai_response, context)

        return {
            "success": True,
            "ai_response": ai_response,
            "execution_result": execution_result,
            "available_tools": [tool["name"] for tool in available_tools]
        }

    def get_available_tools(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of available tools"""
        return self.tool_registry.get_tools_schema()

    def get_tool_documentation(self, tool_name: Optional[str] = None) -> Dict[str, Any]:
        """Get tool documentation"""
        if tool_name:
            tool = self.tool_registry.get_tool(tool_name)
            if tool:
                return tool.definition.to_dict()
            else:
                return {"error": f"Tool '{tool_name}' not found"}
        else:
            return {
                "tools": self.tool_registry.get_tools_schema(),
                "categories": list(set(
                    tool.definition.category
                    for tool in self.tool_registry.tools.values()
                ))
            }

    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get tool execution statistics"""
        registry_stats = self.tool_registry.get_execution_stats()

        # Add service-level statistics
        service_stats = {
            "total_ai_executions": len(self.execution_history),
            "successful_ai_executions": sum(
                1 for exec_log in self.execution_history if exec_log["success"]
            ),
            "average_iterations": (
                sum(len(exec_log["iterations"]) for exec_log in self.execution_history)
                / len(self.execution_history)
            ) if self.execution_history else 0
        }

        return {
            "registry_stats": registry_stats,
            "service_stats": service_stats
        }

    async def _prepare_ai_context(self, context: Dict[str, Any]) -> str:
        """Prepare context information for AI"""
        context_parts = []

        # Project information
        if "project_id" in context:
            project_id = context["project_id"]

            # Get project statistics
            if self.indexing_service.is_project_indexed(project_id):
                stats = self.indexing_service.get_project_statistics(project_id)
                context_parts.append(f"Project Statistics: {stats}")

        # Current working directory
        if "project_root" in context:
            context_parts.append(f"Project Root: {context['project_root']}")

        # Current file context
        if "current_file" in context:
            context_parts.append(f"Current File: {context['current_file']}")

        # Recent errors
        if "recent_errors" in context:
            context_parts.append(f"Recent Errors: {context['recent_errors']}")

        return "\n".join(context_parts) if context_parts else "No additional context available"

    def _is_dangerous_tool(self, tool_name: str) -> bool:
        """Check if a tool is potentially dangerous"""
        tool = self.tool_registry.get_tool(tool_name)
        return tool and tool.definition.dangerous

    async def _generate_follow_up_response(
        self,
        iteration_log: Dict[str, Any],
        context: Dict[str, Any]
    ) -> str:
        """Generate follow-up response for failed tool executions"""

        # Analyze failures
        failed_executions = [
            exec_log for exec_log in iteration_log["tool_executions"]
            if not exec_log["success"]
        ]

        if not failed_executions:
            return ""

        # Create error summary
        error_summary = []
        for failed_exec in failed_executions:
            error_summary.append(
                f"Tool '{failed_exec['tool_name']}' failed: {failed_exec['error']}"
            )

        # Generate follow-up prompt
        follow_up_prompt = f"""
The following tool executions failed:
{chr(10).join(error_summary)}

Please analyze these errors and provide corrected tool calls or alternative approaches.
"""

        # Get AI response for follow-up
        async with OpenRouterClient() as client:
            messages = [
                {"role": "system", "content": "You are debugging failed tool executions. Provide corrected tool calls."},
                {"role": "user", "content": follow_up_prompt}
            ]

            response = await client.create_completion_with_retry(
                messages, temperature=0.1
            )

        if "error" not in response:
            return response["choices"][0]["message"]["content"]

        return ""

    def _compile_final_results(self, execution_log: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Compile final results from all iterations"""
        final_results = []

        for iteration in execution_log["iterations"]:
            for tool_execution in iteration["tool_executions"]:
                if tool_execution["success"]:
                    final_results.append({
                        "tool_name": tool_execution["tool_name"],
                        "result": tool_execution["result"],
                        "success": True
                    })

        return final_results

    def validate_tool_response(self, response: str) -> Dict[str, Any]:
        """Validate an AI response for tool calling"""
        available_tools = [tool["name"] for tool in self.tool_registry.get_tools_schema()]
        return self.json_parser.validate_tool_call_response(response, available_tools)

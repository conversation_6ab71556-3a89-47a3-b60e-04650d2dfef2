import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { ScrollArea } from './ui/scroll-area';
import { Separator } from './ui/separator';
import { 
  Play, 
  Pause, 
  Square, 
  Settings, 
  Activity, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  Brain,
  Zap
} from 'lucide-react';

interface LoopIteration {
  iteration: number;
  phase: string;
  status: string;
  duration: number;
  success: boolean;
  errors: string[];
}

interface LoopStatus {
  loop_id: string;
  status: string;
  current_iteration: number;
  total_iterations: number;
  current_phase?: string;
  error_count: number;
  success_count: number;
  duration: number;
  last_activity?: number;
  recent_iterations: LoopIteration[];
}

interface AgentConsoleProps {
  projectId: number;
}

export const AgentConsole: React.FC<AgentConsoleProps> = ({ projectId }) => {
  const [activeLoops, setActiveLoops] = useState<LoopStatus[]>([]);
  const [selectedLoop, setSelectedLoop] = useState<string | null>(null);
  const [newGoal, setNewGoal] = useState('');
  const [isStarting, setIsStarting] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const logsEndRef = useRef<HTMLDivElement>(null);

  // Fetch active loops
  useEffect(() => {
    const fetchLoops = async () => {
      try {
        const response = await fetch(`/api/agents/loops`);
        if (response.ok) {
          const loops = await response.json();
          setActiveLoops(loops);
        }
      } catch (error) {
        console.error('Failed to fetch loops:', error);
      }
    };

    fetchLoops();
    const interval = setInterval(fetchLoops, 2000); // Update every 2 seconds
    return () => clearInterval(interval);
  }, []);

  // Auto-scroll logs
  useEffect(() => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [logs]);

  const startLoop = async () => {
    if (!newGoal.trim()) return;

    setIsStarting(true);
    try {
      const response = await fetch('/api/agents/start-loop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          project_id: projectId,
          goal: newGoal,
          configuration: {
            max_iterations: 10,
            max_duration_minutes: 60,
            auto_approve_safe_tools: true
          }
        })
      });

      if (response.ok) {
        const result = await response.json();
        addLog(`Started new loop: ${result.loop_id}`);
        setNewGoal('');
      } else {
        addLog('Failed to start loop');
      }
    } catch (error) {
      addLog(`Error starting loop: ${error}`);
    } finally {
      setIsStarting(false);
    }
  };

  const controlLoop = async (loopId: string, action: 'pause' | 'resume' | 'stop') => {
    try {
      const response = await fetch(`/api/agents/loops/${loopId}/${action}`, {
        method: 'POST'
      });

      if (response.ok) {
        addLog(`${action.charAt(0).toUpperCase() + action.slice(1)}d loop: ${loopId}`);
      } else {
        addLog(`Failed to ${action} loop`);
      }
    } catch (error) {
      addLog(`Error ${action}ing loop: ${error}`);
    }
  };

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-500';
      case 'paused': return 'bg-yellow-500';
      case 'stopped': return 'bg-gray-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-blue-500';
    }
  };

  const getPhaseIcon = (phase: string) => {
    switch (phase) {
      case 'analysis': return <Brain className="h-4 w-4" />;
      case 'planning': return <Settings className="h-4 w-4" />;
      case 'execution': return <Zap className="h-4 w-4" />;
      case 'validation': return <CheckCircle className="h-4 w-4" />;
      case 'learning': return <Activity className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      {/* Start New Loop */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Agent Console
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              placeholder="Enter goal for the AI agent (e.g., 'Improve code quality', 'Add unit tests')"
              value={newGoal}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewGoal(e.target.value)}
              onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => e.key === 'Enter' && startLoop()}
              className="flex-1"
            />
            <Button 
              onClick={startLoop} 
              disabled={isStarting || !newGoal.trim()}
              className="flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              Start Loop
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Active Loops */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Active Loops</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-96">
              {activeLoops.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  No active loops
                </div>
              ) : (
                <div className="space-y-4">
                  {activeLoops.map((loop) => (
                    <div
                      key={loop.loop_id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedLoop === loop.loop_id ? 'bg-accent' : 'hover:bg-accent/50'
                      }`}
                      onClick={() => setSelectedLoop(loop.loop_id)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${getStatusColor(loop.status)}`} />
                          <span className="font-medium">Loop {loop.loop_id.slice(0, 8)}</span>
                          <Badge variant="outline">{loop.status}</Badge>
                        </div>
                        <div className="flex gap-1">
                          {loop.status === 'running' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e: React.MouseEvent) => {
                                e.stopPropagation();
                                controlLoop(loop.loop_id, 'pause');
                              }}
                            >
                              <Pause className="h-3 w-3" />
                            </Button>
                          )}
                          {loop.status === 'paused' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e: React.MouseEvent) => {
                                e.stopPropagation();
                                controlLoop(loop.loop_id, 'resume');
                              }}
                            >
                              <Play className="h-3 w-3" />
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e: React.MouseEvent) => {
                              e.stopPropagation();
                              controlLoop(loop.loop_id, 'stop');
                            }}
                          >
                            <Square className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Iteration:</span>
                          <span className="ml-1">{loop.current_iteration}/{loop.total_iterations}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Duration:</span>
                          <span className="ml-1">{formatDuration(loop.duration)}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Success:</span>
                          <span className="ml-1 text-green-600">{loop.success_count}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Errors:</span>
                          <span className="ml-1 text-red-600">{loop.error_count}</span>
                        </div>
                      </div>

                      {loop.current_phase && (
                        <div className="flex items-center gap-2 mt-2">
                          {getPhaseIcon(loop.current_phase)}
                          <span className="text-sm capitalize">{loop.current_phase}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Loop Details */}
        <Card>
          <CardHeader>
            <CardTitle>Loop Details</CardTitle>
          </CardHeader>
          <CardContent>
            {selectedLoop ? (
              <ScrollArea className="h-96">
                {(() => {
                  const loop = activeLoops.find(l => l.loop_id === selectedLoop);
                  if (!loop) return <div>Loop not found</div>;

                  return (
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2">Recent Iterations</h4>
                        <div className="space-y-2">
                          {loop.recent_iterations.map((iteration) => (
                            <div key={iteration.iteration} className="p-3 border rounded">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center gap-2">
                                  <span className="font-medium">#{iteration.iteration}</span>
                                  {getPhaseIcon(iteration.phase)}
                                  <span className="text-sm capitalize">{iteration.phase}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  {iteration.success ? (
                                    <CheckCircle className="h-4 w-4 text-green-500" />
                                  ) : (
                                    <AlertCircle className="h-4 w-4 text-red-500" />
                                  )}
                                  <span className="text-sm">{formatDuration(iteration.duration)}</span>
                                </div>
                              </div>
                              {iteration.errors.length > 0 && (
                                <div className="text-sm text-red-600">
                                  {iteration.errors.slice(0, 2).map((error, i) => (
                                    <div key={i}>• {error}</div>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </ScrollArea>
            ) : (
              <div className="text-center text-muted-foreground py-8">
                Select a loop to view details
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Logs */}
      <Card>
        <CardHeader>
          <CardTitle>Agent Logs</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-48">
            <div className="space-y-1 font-mono text-sm">
              {logs.map((log, index) => (
                <div key={index} className="text-muted-foreground">
                  {log}
                </div>
              ))}
              <div ref={logsEndRef} />
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};

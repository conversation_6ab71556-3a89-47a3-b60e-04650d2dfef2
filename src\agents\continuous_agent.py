"""
Continuous Agent for autonomous coding loops
"""
import asyncio
import time
import uuid
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from sqlalchemy.orm import Session

from src.services.tool_service import ToolExecutionService
from src.services.ai_service import AIService
from src.indexing.indexing_service import IndexingService
from src.config.logging_config import get_logger

logger = get_logger(__name__)


class LoopPhase(Enum):
    """Phases of the continuous loop"""
    ANALYSIS = "analysis"
    PLANNING = "planning"
    EXECUTION = "execution"
    VALIDATION = "validation"
    LEARNING = "learning"


class LoopStatus(Enum):
    """Status of the continuous loop"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class LoopIteration:
    """Represents a single loop iteration"""
    iteration_number: int
    start_time: float
    end_time: Optional[float] = None
    phase: LoopPhase = LoopPhase.ANALYSIS
    status: str = "running"
    analysis_results: Dict[str, Any] = field(default_factory=dict)
    plan: Dict[str, Any] = field(default_factory=dict)
    execution_results: Dict[str, Any] = field(default_factory=dict)
    validation_results: Dict[str, Any] = field(default_factory=dict)
    learning_insights: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    success: bool = False
    
    def get_duration(self) -> float:
        """Get iteration duration"""
        if self.end_time:
            return self.end_time - self.start_time
        return time.time() - self.start_time


@dataclass
class LoopConfiguration:
    """Configuration for the continuous loop"""
    max_iterations: int = 10
    max_duration_minutes: int = 60
    auto_approve_safe_tools: bool = True
    error_threshold: int = 3  # Max consecutive errors before stopping
    success_criteria: List[str] = field(default_factory=list)
    focus_areas: List[str] = field(default_factory=list)
    excluded_files: List[str] = field(default_factory=list)
    validation_commands: List[str] = field(default_factory=list)


@dataclass
class LoopState:
    """Current state of the continuous loop"""
    loop_id: str
    project_id: int
    status: LoopStatus = LoopStatus.IDLE
    current_iteration: int = 0
    total_iterations: int = 0
    start_time: Optional[float] = None
    last_activity: Optional[float] = None
    configuration: LoopConfiguration = field(default_factory=LoopConfiguration)
    iterations: List[LoopIteration] = field(default_factory=list)
    error_count: int = 0
    success_count: int = 0
    current_phase: Optional[LoopPhase] = None
    context: Dict[str, Any] = field(default_factory=dict)


class ContinuousAgent:
    """Autonomous coding agent with continuous improvement loops"""
    
    def __init__(self, db: Session):
        self.db = db
        self.tool_service = ToolExecutionService(db)
        self.ai_service = AIService(db)
        self.indexing_service = IndexingService(db)
        
        # Loop management
        self.active_loops: Dict[str, LoopState] = {}
        self.loop_callbacks: Dict[str, List[Callable]] = {}
        
        # Performance tracking
        self.performance_history: List[Dict[str, Any]] = []
    
    async def start_loop(
        self,
        project_id: int,
        configuration: LoopConfiguration,
        initial_goal: str,
        context: Dict[str, Any] = None
    ) -> str:
        """Start a new continuous loop"""
        
        loop_id = str(uuid.uuid4())
        
        # Create loop state
        loop_state = LoopState(
            loop_id=loop_id,
            project_id=project_id,
            configuration=configuration,
            context=context or {}
        )
        
        # Add initial goal to context
        loop_state.context["initial_goal"] = initial_goal
        loop_state.context["project_root"] = context.get("project_root", ".")
        
        # Store loop state
        self.active_loops[loop_id] = loop_state
        
        # Start the loop in background
        asyncio.create_task(self._run_loop(loop_id))
        
        logger.info(f"Started continuous loop {loop_id} for project {project_id}")
        return loop_id
    
    async def stop_loop(self, loop_id: str) -> bool:
        """Stop a running loop"""
        if loop_id not in self.active_loops:
            return False
        
        loop_state = self.active_loops[loop_id]
        loop_state.status = LoopStatus.STOPPED
        
        logger.info(f"Stopped continuous loop {loop_id}")
        return True
    
    async def pause_loop(self, loop_id: str) -> bool:
        """Pause a running loop"""
        if loop_id not in self.active_loops:
            return False
        
        loop_state = self.active_loops[loop_id]
        if loop_state.status == LoopStatus.RUNNING:
            loop_state.status = LoopStatus.PAUSED
            logger.info(f"Paused continuous loop {loop_id}")
            return True
        
        return False
    
    async def resume_loop(self, loop_id: str) -> bool:
        """Resume a paused loop"""
        if loop_id not in self.active_loops:
            return False
        
        loop_state = self.active_loops[loop_id]
        if loop_state.status == LoopStatus.PAUSED:
            loop_state.status = LoopStatus.RUNNING
            logger.info(f"Resumed continuous loop {loop_id}")
            return True
        
        return False
    
    def get_loop_status(self, loop_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of a loop"""
        if loop_id not in self.active_loops:
            return None
        
        loop_state = self.active_loops[loop_id]
        
        return {
            "loop_id": loop_id,
            "status": loop_state.status.value,
            "current_iteration": loop_state.current_iteration,
            "total_iterations": loop_state.total_iterations,
            "current_phase": loop_state.current_phase.value if loop_state.current_phase else None,
            "error_count": loop_state.error_count,
            "success_count": loop_state.success_count,
            "duration": time.time() - loop_state.start_time if loop_state.start_time else 0,
            "last_activity": loop_state.last_activity,
            "configuration": loop_state.configuration.__dict__,
            "recent_iterations": [
                {
                    "iteration": iter.iteration_number,
                    "phase": iter.phase.value,
                    "status": iter.status,
                    "duration": iter.get_duration(),
                    "success": iter.success,
                    "errors": iter.errors
                }
                for iter in loop_state.iterations[-5:]  # Last 5 iterations
            ]
        }
    
    def list_active_loops(self) -> List[Dict[str, Any]]:
        """List all active loops"""
        return [
            self.get_loop_status(loop_id) 
            for loop_id in self.active_loops.keys()
        ]
    
    async def _run_loop(self, loop_id: str):
        """Main loop execution"""
        loop_state = self.active_loops[loop_id]
        
        try:
            loop_state.status = LoopStatus.RUNNING
            loop_state.start_time = time.time()
            loop_state.last_activity = time.time()
            
            while self._should_continue_loop(loop_state):
                # Check if paused
                while loop_state.status == LoopStatus.PAUSED:
                    await asyncio.sleep(1)
                
                # Check if stopped
                if loop_state.status == LoopStatus.STOPPED:
                    break
                
                # Start new iteration
                iteration = LoopIteration(
                    iteration_number=loop_state.current_iteration + 1,
                    start_time=time.time()
                )
                
                loop_state.current_iteration += 1
                loop_state.current_phase = LoopPhase.ANALYSIS
                
                try:
                    # Execute loop phases
                    await self._execute_analysis_phase(loop_state, iteration)
                    await self._execute_planning_phase(loop_state, iteration)
                    await self._execute_execution_phase(loop_state, iteration)
                    await self._execute_validation_phase(loop_state, iteration)
                    await self._execute_learning_phase(loop_state, iteration)
                    
                    # Mark iteration as successful
                    iteration.success = True
                    iteration.status = "completed"
                    loop_state.success_count += 1
                    loop_state.error_count = 0  # Reset error count on success
                    
                except Exception as e:
                    # Handle iteration error
                    iteration.errors.append(str(e))
                    iteration.status = "failed"
                    loop_state.error_count += 1
                    
                    logger.error(f"Loop iteration failed: {e}")
                    
                    # Check error threshold
                    if loop_state.error_count >= loop_state.configuration.error_threshold:
                        logger.error(f"Loop {loop_id} stopped due to too many errors")
                        loop_state.status = LoopStatus.ERROR
                        break
                
                # Finalize iteration
                iteration.end_time = time.time()
                loop_state.iterations.append(iteration)
                loop_state.total_iterations += 1
                loop_state.last_activity = time.time()
                
                # Notify callbacks
                await self._notify_callbacks(loop_id, "iteration_completed", iteration)
                
                # Brief pause between iterations
                await asyncio.sleep(2)
            
            # Loop completed
            if loop_state.status == LoopStatus.RUNNING:
                loop_state.status = LoopStatus.IDLE
            
            logger.info(f"Loop {loop_id} completed with {loop_state.success_count} successes and {loop_state.error_count} errors")
            
        except Exception as e:
            logger.error(f"Fatal error in loop {loop_id}: {e}")
            loop_state.status = LoopStatus.ERROR
        
        finally:
            # Cleanup
            await self._notify_callbacks(loop_id, "loop_completed", loop_state)
    
    def _should_continue_loop(self, loop_state: LoopState) -> bool:
        """Check if loop should continue"""
        config = loop_state.configuration
        
        # Check iteration limit
        if loop_state.current_iteration >= config.max_iterations:
            return False
        
        # Check time limit
        if loop_state.start_time:
            duration_minutes = (time.time() - loop_state.start_time) / 60
            if duration_minutes >= config.max_duration_minutes:
                return False
        
        # Check if stopped or error
        if loop_state.status in [LoopStatus.STOPPED, LoopStatus.ERROR]:
            return False
        
        # Check success criteria
        if config.success_criteria and self._check_success_criteria(loop_state):
            return False
        
        return True

    async def _execute_analysis_phase(self, loop_state: LoopState, iteration: LoopIteration):
        """Execute the analysis phase"""
        iteration.phase = LoopPhase.ANALYSIS
        loop_state.current_phase = LoopPhase.ANALYSIS

        logger.debug(f"Starting analysis phase for iteration {iteration.iteration_number}")

        # Analyze current project state
        analysis_prompt = f"""
Analyze the current state of the project and identify areas for improvement.

Project ID: {loop_state.project_id}
Current Goal: {loop_state.context.get('initial_goal', 'General improvement')}
Iteration: {iteration.iteration_number}

Please analyze:
1. Code quality issues
2. Potential bugs or errors
3. Performance improvements
4. Missing functionality
5. Test coverage gaps

Provide a structured analysis with specific actionable items.
"""

        # Get AI analysis
        analysis_result = await self.ai_service.analyze_code(
            loop_state.project_id,
            loop_state.context.get("current_file", ""),
            analysis_type="general"
        )

        if analysis_result["success"]:
            iteration.analysis_results = {
                "ai_analysis": analysis_result["analysis"],
                "focus_areas": self._extract_focus_areas(analysis_result["analysis"]),
                "priority_items": self._extract_priority_items(analysis_result["analysis"])
            }
        else:
            iteration.errors.append(f"Analysis failed: {analysis_result.get('error', 'Unknown error')}")

        # Add project statistics
        if self.indexing_service.is_project_indexed(loop_state.project_id):
            stats = self.indexing_service.get_project_statistics(loop_state.project_id)
            iteration.analysis_results["project_stats"] = stats

        logger.debug(f"Analysis phase completed for iteration {iteration.iteration_number}")

    async def _execute_planning_phase(self, loop_state: LoopState, iteration: LoopIteration):
        """Execute the planning phase"""
        iteration.phase = LoopPhase.PLANNING
        loop_state.current_phase = LoopPhase.PLANNING

        logger.debug(f"Starting planning phase for iteration {iteration.iteration_number}")

        # Create plan based on analysis
        planning_prompt = f"""
Based on the analysis results, create a specific action plan for this iteration.

Analysis Results: {iteration.analysis_results}
Previous Iterations: {len(loop_state.iterations)}
Success Count: {loop_state.success_count}
Error Count: {loop_state.error_count}

Create a plan that includes:
1. Specific tasks to accomplish
2. Files to modify or create
3. Tools to use
4. Success criteria for this iteration
5. Risk assessment

Focus on incremental improvements that can be completed in one iteration.
"""

        # Get AI planning
        context = {
            "project_id": loop_state.project_id,
            "project_root": loop_state.context.get("project_root", "."),
            "indexing_service": self.indexing_service
        }

        planning_result = await self.tool_service.get_ai_tool_response(
            planning_prompt, context, include_tools=False
        )

        if planning_result["success"]:
            plan_text = planning_result["ai_response"]
            iteration.plan = {
                "plan_text": plan_text,
                "tasks": self._extract_tasks_from_plan(plan_text),
                "target_files": self._extract_target_files(plan_text),
                "tools_needed": self._extract_tools_needed(plan_text),
                "success_criteria": self._extract_success_criteria(plan_text)
            }
        else:
            iteration.errors.append(f"Planning failed: {planning_result.get('error', 'Unknown error')}")

        logger.debug(f"Planning phase completed for iteration {iteration.iteration_number}")

    async def _execute_execution_phase(self, loop_state: LoopState, iteration: LoopIteration):
        """Execute the execution phase"""
        iteration.phase = LoopPhase.EXECUTION
        loop_state.current_phase = LoopPhase.EXECUTION

        logger.debug(f"Starting execution phase for iteration {iteration.iteration_number}")

        if not iteration.plan:
            iteration.errors.append("No plan available for execution")
            return

        # Execute the plan using tools
        execution_prompt = f"""
Execute the following plan using available tools:

Plan: {iteration.plan['plan_text']}
Tasks: {iteration.plan.get('tasks', [])}
Target Files: {iteration.plan.get('target_files', [])}

Use the appropriate tools to implement the planned changes. Be careful and make incremental changes.
"""

        context = {
            "project_id": loop_state.project_id,
            "project_root": loop_state.context.get("project_root", "."),
            "indexing_service": self.indexing_service,
            "current_iteration": iteration.iteration_number
        }

        # Execute with tools
        execution_result = await self.tool_service.get_ai_tool_response(
            execution_prompt,
            context,
            include_tools=True
        )

        if execution_result["success"]:
            iteration.execution_results = {
                "ai_response": execution_result["ai_response"],
                "tool_execution": execution_result["execution_result"],
                "tools_used": execution_result.get("available_tools", [])
            }

            # Check if execution was successful
            if execution_result["execution_result"]["success"]:
                iteration.execution_results["changes_made"] = self._extract_changes_made(
                    execution_result["execution_result"]
                )
            else:
                iteration.errors.append("Tool execution failed")
        else:
            iteration.errors.append(f"Execution failed: {execution_result.get('error', 'Unknown error')}")

        logger.debug(f"Execution phase completed for iteration {iteration.iteration_number}")

    async def _execute_validation_phase(self, loop_state: LoopState, iteration: LoopIteration):
        """Execute the validation phase"""
        iteration.phase = LoopPhase.VALIDATION
        loop_state.current_phase = LoopPhase.VALIDATION

        logger.debug(f"Starting validation phase for iteration {iteration.iteration_number}")

        # Validate the changes made
        validation_results = {
            "syntax_check": await self._validate_syntax(loop_state, iteration),
            "test_results": await self._run_tests(loop_state, iteration),
            "quality_check": await self._check_code_quality(loop_state, iteration)
        }

        # Overall validation success
        validation_success = all([
            validation_results["syntax_check"].get("success", False),
            validation_results["test_results"].get("success", True),  # Tests optional
            validation_results["quality_check"].get("success", True)   # Quality optional
        ])

        iteration.validation_results = {
            **validation_results,
            "overall_success": validation_success
        }

        if not validation_success:
            iteration.errors.append("Validation failed")

        logger.debug(f"Validation phase completed for iteration {iteration.iteration_number}")

    async def _execute_learning_phase(self, loop_state: LoopState, iteration: LoopIteration):
        """Execute the learning phase"""
        iteration.phase = LoopPhase.LEARNING
        loop_state.current_phase = LoopPhase.LEARNING

        logger.debug(f"Starting learning phase for iteration {iteration.iteration_number}")

        # Learn from this iteration
        learning_prompt = f"""
Analyze this iteration and extract learning insights:

Iteration: {iteration.iteration_number}
Analysis: {iteration.analysis_results}
Plan: {iteration.plan}
Execution: {iteration.execution_results}
Validation: {iteration.validation_results}
Errors: {iteration.errors}
Success: {iteration.success}

What can be learned from this iteration? What worked well? What should be improved?
Provide insights for future iterations.
"""

        context = {
            "project_id": loop_state.project_id,
            "project_root": loop_state.context.get("project_root", "."),
        }

        learning_result = await self.tool_service.get_ai_tool_response(
            learning_prompt, context, include_tools=False
        )

        if learning_result["success"]:
            iteration.learning_insights = {
                "insights": learning_result["ai_response"],
                "success_factors": self._extract_success_factors(learning_result["ai_response"]),
                "improvement_areas": self._extract_improvement_areas(learning_result["ai_response"])
            }

            # Update loop context with learnings
            if "learnings" not in loop_state.context:
                loop_state.context["learnings"] = []

            loop_state.context["learnings"].append(iteration.learning_insights)
        else:
            iteration.errors.append(f"Learning failed: {learning_result.get('error', 'Unknown error')}")

        logger.debug(f"Learning phase completed for iteration {iteration.iteration_number}")

    def _check_success_criteria(self, loop_state: LoopState) -> bool:
        """Check if success criteria are met"""
        # TODO: Implement success criteria checking
        return False

    async def _notify_callbacks(self, loop_id: str, event: str, data: Any):
        """Notify registered callbacks"""
        if loop_id in self.loop_callbacks:
            for callback in self.loop_callbacks[loop_id]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(event, data)
                    else:
                        callback(event, data)
                except Exception as e:
                    logger.error(f"Callback error: {e}")

    # Helper methods for extracting information from AI responses
    def _extract_focus_areas(self, analysis_text: str) -> List[str]:
        """Extract focus areas from analysis"""
        # Simple keyword extraction - could be enhanced with NLP
        focus_areas = []
        keywords = ["bug", "error", "performance", "quality", "test", "security", "refactor"]

        for keyword in keywords:
            if keyword.lower() in analysis_text.lower():
                focus_areas.append(keyword)

        return focus_areas

    def _extract_priority_items(self, analysis_text: str) -> List[str]:
        """Extract priority items from analysis"""
        # Simple extraction - look for numbered lists or bullet points
        lines = analysis_text.split('\n')
        priority_items = []

        for line in lines:
            line = line.strip()
            if line.startswith(('1.', '2.', '3.', '-', '*')):
                priority_items.append(line)

        return priority_items[:5]  # Top 5 items

    def _extract_tasks_from_plan(self, plan_text: str) -> List[str]:
        """Extract tasks from plan"""
        return self._extract_priority_items(plan_text)  # Same logic

    def _extract_target_files(self, plan_text: str) -> List[str]:
        """Extract target files from plan"""
        import re
        # Look for file patterns
        file_patterns = re.findall(r'[\w/]+\.\w+', plan_text)
        return list(set(file_patterns))  # Remove duplicates

    def _extract_tools_needed(self, plan_text: str) -> List[str]:
        """Extract tools needed from plan"""
        tools = []
        tool_keywords = ["read_file", "write_file", "run_command", "search_codebase", "analyze_error"]

        for tool in tool_keywords:
            if tool in plan_text.lower():
                tools.append(tool)

        return tools

    def _extract_success_criteria(self, plan_text: str) -> List[str]:
        """Extract success criteria from plan"""
        lines = plan_text.split('\n')
        criteria = []

        for line in lines:
            if 'success' in line.lower() or 'criteria' in line.lower():
                criteria.append(line.strip())

        return criteria

    def _extract_changes_made(self, execution_result: Dict[str, Any]) -> List[str]:
        """Extract changes made from execution result"""
        changes = []

        if "final_results" in execution_result:
            for result in execution_result["final_results"]:
                if result.get("success"):
                    tool_name = result.get("tool_name", "unknown")
                    changes.append(f"Used {tool_name}")

        return changes

    def _extract_success_factors(self, learning_text: str) -> List[str]:
        """Extract success factors from learning"""
        return self._extract_priority_items(learning_text)

    def _extract_improvement_areas(self, learning_text: str) -> List[str]:
        """Extract improvement areas from learning"""
        lines = learning_text.split('\n')
        improvements = []

        for line in lines:
            if 'improve' in line.lower() or 'better' in line.lower():
                improvements.append(line.strip())

        return improvements[:3]  # Top 3

    async def _validate_syntax(self, loop_state: LoopState, iteration: LoopIteration) -> Dict[str, Any]:
        """Validate syntax of modified files"""
        try:
            # Get list of modified files from execution results
            target_files = iteration.plan.get("target_files", [])

            if not target_files:
                return {"success": True, "message": "No files to validate"}

            # Use run_command tool to check syntax
            context = {
                "project_id": loop_state.project_id,
                "project_root": loop_state.context.get("project_root", "."),
            }

            validation_results = []

            for file_path in target_files:
                if file_path.endswith('.py'):
                    # Python syntax check
                    result = await self.tool_service.execute_tool_directly(
                        "run_command",
                        {"command": f"python -m py_compile {file_path}"},
                        context
                    )

                    validation_results.append({
                        "file": file_path,
                        "success": result.success,
                        "error": result.error
                    })
                elif file_path.endswith(('.js', '.ts')):
                    # JavaScript/TypeScript syntax check (if node is available)
                    result = await self.tool_service.execute_tool_directly(
                        "run_command",
                        {"command": f"node -c {file_path}"},
                        context
                    )

                    validation_results.append({
                        "file": file_path,
                        "success": result.success,
                        "error": result.error
                    })

            overall_success = all(result["success"] for result in validation_results)

            return {
                "success": overall_success,
                "results": validation_results,
                "message": "Syntax validation completed"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Syntax validation failed"
            }

    async def _run_tests(self, loop_state: LoopState, iteration: LoopIteration) -> Dict[str, Any]:
        """Run tests if available"""
        try:
            context = {
                "project_id": loop_state.project_id,
                "project_root": loop_state.context.get("project_root", "."),
            }

            # Try common test commands
            test_commands = [
                "python -m pytest",
                "npm test",
                "python -m unittest discover",
                "cargo test"
            ]

            for command in test_commands:
                result = await self.tool_service.execute_tool_directly(
                    "run_command",
                    {"command": command, "timeout": 60},
                    context
                )

                if result.success:
                    return {
                        "success": True,
                        "command": command,
                        "output": result.result,
                        "message": "Tests passed"
                    }

            # No tests found or all failed
            return {
                "success": True,  # Don't fail if no tests
                "message": "No tests found or tests failed",
                "optional": True
            }

        except Exception as e:
            return {
                "success": True,  # Don't fail validation on test errors
                "error": str(e),
                "message": "Test execution failed",
                "optional": True
            }

    async def _check_code_quality(self, loop_state: LoopState, iteration: LoopIteration) -> Dict[str, Any]:
        """Check code quality"""
        try:
            # Simple quality check - could be enhanced with linters
            target_files = iteration.plan.get("target_files", [])

            if not target_files:
                return {"success": True, "message": "No files to check"}

            quality_issues = []

            for file_path in target_files:
                # Read file and do basic checks
                context = {
                    "project_id": loop_state.project_id,
                    "project_root": loop_state.context.get("project_root", "."),
                }

                result = await self.tool_service.execute_tool_directly(
                    "read_file",
                    {"file_path": file_path},
                    context
                )

                if result.success:
                    content = result.result.get("content", "")

                    # Basic quality checks
                    lines = content.split('\n')

                    # Check for very long lines
                    long_lines = [i+1 for i, line in enumerate(lines) if len(line) > 120]
                    if long_lines:
                        quality_issues.append(f"{file_path}: Long lines at {long_lines[:3]}")

                    # Check for TODO/FIXME comments
                    todo_lines = [i+1 for i, line in enumerate(lines) if 'TODO' in line or 'FIXME' in line]
                    if todo_lines:
                        quality_issues.append(f"{file_path}: TODO/FIXME at lines {todo_lines[:3]}")

            return {
                "success": len(quality_issues) < 5,  # Allow some issues
                "issues": quality_issues,
                "message": f"Found {len(quality_issues)} quality issues"
            }

        except Exception as e:
            return {
                "success": True,  # Don't fail validation on quality check errors
                "error": str(e),
                "message": "Quality check failed",
                "optional": True
            }

"""
Celery application configuration
"""
from celery import Celery
from config.settings import settings

# Create Celery app
celery_app = Celery(
    "echocode",
    broker=settings.redis_url,
    backend=settings.redis_url,
    include=["src.tasks.agent_tasks", "src.tasks.indexing_tasks"]
)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Auto-discover tasks
celery_app.autodiscover_tasks()

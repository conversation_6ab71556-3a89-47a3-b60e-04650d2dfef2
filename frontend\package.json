{"name": "echocode-frontend", "version": "1.0.0", "description": "EchoCode AI Coder Frontend", "private": true, "dependencies": {"@monaco-editor/react": "^4.6.0", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-tabs": "^1.1.12", "@tanstack/react-query": "^4.36.1", "@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "ajv": "^8.17.1", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-router-dom": "^6.20.1", "react-scripts": "^5.0.1", "react-split": "^2.0.14", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "web-vitals": "^3.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}
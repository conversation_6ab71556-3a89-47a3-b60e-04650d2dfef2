"""
Error Analysis Service for intelligent error detection and resolution
"""
import re
import time
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from sqlalchemy.orm import Session

from src.services.ai_service import AIService
from src.services.tool_service import ToolExecutionService
from src.indexing.indexing_service import IndexingService
from src.config.logging_config import get_logger

logger = get_logger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories"""
    SYNTAX = "syntax"
    RUNTIME = "runtime"
    LOGICAL = "logical"
    PERFORMANCE = "performance"
    SECURITY = "security"
    DEPENDENCY = "dependency"
    CONFIGURATION = "configuration"
    UNKNOWN = "unknown"


@dataclass
class ErrorPattern:
    """Represents a recognized error pattern"""
    pattern_id: str
    name: str
    regex_patterns: List[str]
    category: ErrorCategory
    severity: ErrorSeverity
    common_causes: List[str]
    resolution_steps: List[str]
    confidence_threshold: float = 0.7


@dataclass
class ErrorInstance:
    """Represents a specific error occurrence"""
    error_id: str
    message: str
    file_path: Optional[str]
    line_number: Optional[int]
    stack_trace: Optional[str]
    category: ErrorCategory
    severity: ErrorSeverity
    timestamp: float
    project_id: int
    context: Dict[str, Any] = field(default_factory=dict)
    pattern_matches: List[str] = field(default_factory=list)
    resolution_attempts: List[Dict[str, Any]] = field(default_factory=list)
    resolved: bool = False
    resolution_confidence: float = 0.0


@dataclass
class ErrorAnalysisResult:
    """Result of error analysis"""
    error_instance: ErrorInstance
    analysis: str
    suggested_fixes: List[str]
    related_errors: List[str]
    confidence: float
    automated_fix_available: bool = False
    fix_commands: List[str] = field(default_factory=list)


class ErrorAnalysisService:
    """Service for analyzing and resolving errors"""
    
    def __init__(self, db: Session):
        self.db = db
        self.ai_service = AIService(db)
        self.tool_service = ToolExecutionService(db)
        self.indexing_service = IndexingService(db)
        
        # Error tracking
        self.error_history: Dict[str, ErrorInstance] = {}
        self.error_patterns: Dict[str, ErrorPattern] = {}
        self.pattern_statistics: Dict[str, Dict[str, Any]] = {}
        
        # Initialize built-in patterns
        self._initialize_error_patterns()
    
    def _initialize_error_patterns(self):
        """Initialize built-in error patterns"""
        patterns = [
            ErrorPattern(
                pattern_id="python_syntax_error",
                name="Python Syntax Error",
                regex_patterns=[
                    r"SyntaxError: (.+)",
                    r"IndentationError: (.+)",
                    r"TabError: (.+)"
                ],
                category=ErrorCategory.SYNTAX,
                severity=ErrorSeverity.HIGH,
                common_causes=[
                    "Missing parentheses, brackets, or quotes",
                    "Incorrect indentation",
                    "Mixed tabs and spaces",
                    "Invalid syntax"
                ],
                resolution_steps=[
                    "Check for missing or extra parentheses",
                    "Verify proper indentation",
                    "Use consistent whitespace (tabs or spaces)",
                    "Check for typos in keywords"
                ]
            ),
            ErrorPattern(
                pattern_id="python_name_error",
                name="Python Name Error",
                regex_patterns=[
                    r"NameError: name '(.+)' is not defined",
                    r"NameError: (.+)"
                ],
                category=ErrorCategory.RUNTIME,
                severity=ErrorSeverity.MEDIUM,
                common_causes=[
                    "Variable used before definition",
                    "Typo in variable name",
                    "Missing import statement",
                    "Scope issues"
                ],
                resolution_steps=[
                    "Check if variable is defined before use",
                    "Verify variable name spelling",
                    "Add necessary import statements",
                    "Check variable scope"
                ]
            ),
            ErrorPattern(
                pattern_id="python_import_error",
                name="Python Import Error",
                regex_patterns=[
                    r"ImportError: (.+)",
                    r"ModuleNotFoundError: No module named '(.+)'"
                ],
                category=ErrorCategory.DEPENDENCY,
                severity=ErrorSeverity.HIGH,
                common_causes=[
                    "Missing package installation",
                    "Incorrect module name",
                    "Path issues",
                    "Virtual environment not activated"
                ],
                resolution_steps=[
                    "Install missing package with pip",
                    "Check module name spelling",
                    "Verify Python path",
                    "Activate correct virtual environment"
                ]
            ),
            ErrorPattern(
                pattern_id="javascript_reference_error",
                name="JavaScript Reference Error",
                regex_patterns=[
                    r"ReferenceError: (.+) is not defined",
                    r"ReferenceError: (.+)"
                ],
                category=ErrorCategory.RUNTIME,
                severity=ErrorSeverity.MEDIUM,
                common_causes=[
                    "Variable used before declaration",
                    "Typo in variable name",
                    "Missing script import",
                    "Scope issues"
                ],
                resolution_steps=[
                    "Declare variable before use",
                    "Check variable name spelling",
                    "Add script imports",
                    "Check variable scope"
                ]
            ),
            ErrorPattern(
                pattern_id="type_error",
                name="Type Error",
                regex_patterns=[
                    r"TypeError: (.+)",
                    r"AttributeError: (.+) has no attribute '(.+)'"
                ],
                category=ErrorCategory.RUNTIME,
                severity=ErrorSeverity.MEDIUM,
                common_causes=[
                    "Incorrect data type usage",
                    "Missing method or attribute",
                    "Null/undefined values",
                    "Type conversion issues"
                ],
                resolution_steps=[
                    "Check data types",
                    "Verify object methods and attributes",
                    "Add null/undefined checks",
                    "Use proper type conversion"
                ]
            )
        ]
        
        for pattern in patterns:
            self.error_patterns[pattern.pattern_id] = pattern
    
    async def analyze_error(
        self,
        error_message: str,
        project_id: int,
        file_path: Optional[str] = None,
        line_number: Optional[int] = None,
        stack_trace: Optional[str] = None,
        context: Dict[str, Any] = None
    ) -> ErrorAnalysisResult:
        """Analyze an error and provide resolution suggestions"""
        
        # Create error instance
        error_id = self._generate_error_id(error_message, file_path, line_number)
        
        error_instance = ErrorInstance(
            error_id=error_id,
            message=error_message,
            file_path=file_path,
            line_number=line_number,
            stack_trace=stack_trace,
            category=ErrorCategory.UNKNOWN,
            severity=ErrorSeverity.MEDIUM,
            timestamp=time.time(),
            project_id=project_id,
            context=context or {}
        )
        
        # Pattern matching
        matched_patterns = self._match_error_patterns(error_message)
        error_instance.pattern_matches = [p.pattern_id for p in matched_patterns]
        
        # Categorize and assess severity
        if matched_patterns:
            # Use the most confident pattern match
            best_pattern = max(matched_patterns, key=lambda p: p.confidence_threshold)
            error_instance.category = best_pattern.category
            error_instance.severity = best_pattern.severity
        else:
            # Use AI to categorize unknown errors
            error_instance.category, error_instance.severity = await self._ai_categorize_error(error_message)
        
        # Store error instance
        self.error_history[error_id] = error_instance
        
        # Get AI analysis
        ai_analysis = await self._get_ai_error_analysis(error_instance)
        
        # Find related errors
        related_errors = self._find_related_errors(error_instance)
        
        # Generate suggested fixes
        suggested_fixes = self._generate_suggested_fixes(error_instance, matched_patterns)
        
        # Check for automated fix availability
        automated_fix_available, fix_commands = await self._check_automated_fix(error_instance)
        
        # Calculate confidence
        confidence = self._calculate_analysis_confidence(error_instance, matched_patterns, ai_analysis)
        
        result = ErrorAnalysisResult(
            error_instance=error_instance,
            analysis=ai_analysis,
            suggested_fixes=suggested_fixes,
            related_errors=related_errors,
            confidence=confidence,
            automated_fix_available=automated_fix_available,
            fix_commands=fix_commands
        )
        
        # Update statistics
        self._update_pattern_statistics(matched_patterns)
        
        logger.info(f"Analyzed error {error_id} with confidence {confidence:.2f}")
        return result
    
    async def attempt_automated_fix(
        self,
        error_id: str,
        apply_fix: bool = False
    ) -> Dict[str, Any]:
        """Attempt to automatically fix an error"""
        
        if error_id not in self.error_history:
            return {"success": False, "error": "Error not found"}
        
        error_instance = self.error_history[error_id]
        
        # Get error analysis if not already done
        analysis_result = await self.analyze_error(
            error_instance.message,
            error_instance.project_id,
            error_instance.file_path,
            error_instance.line_number,
            error_instance.stack_trace,
            error_instance.context
        )
        
        if not analysis_result.automated_fix_available:
            return {"success": False, "error": "No automated fix available"}
        
        # Prepare context for tool execution
        context = {
            "project_id": error_instance.project_id,
            "project_root": error_instance.context.get("project_root", "."),
            "error_instance": error_instance,
            "analysis_result": analysis_result
        }
        
        fix_results = []
        
        for command in analysis_result.fix_commands:
            if apply_fix:
                # Execute the fix command
                result = await self.tool_service.get_ai_tool_response(
                    f"Execute this fix for the error: {command}",
                    context,
                    include_tools=True
                )
                
                fix_results.append({
                    "command": command,
                    "executed": True,
                    "success": result.get("success", False),
                    "result": result
                })
            else:
                # Just return what would be executed
                fix_results.append({
                    "command": command,
                    "executed": False,
                    "preview": True
                })
        
        # Record the attempt
        attempt = {
            "timestamp": time.time(),
            "automated": True,
            "applied": apply_fix,
            "commands": analysis_result.fix_commands,
            "results": fix_results
        }
        
        error_instance.resolution_attempts.append(attempt)
        
        # Check if error was resolved
        if apply_fix:
            overall_success = all(result.get("success", False) for result in fix_results)
            if overall_success:
                error_instance.resolved = True
                error_instance.resolution_confidence = analysis_result.confidence
        
        return {
            "success": True,
            "error_id": error_id,
            "fix_results": fix_results,
            "applied": apply_fix,
            "resolved": error_instance.resolved
        }

    def get_error_statistics(self, project_id: Optional[int] = None) -> Dict[str, Any]:
        """Get error analysis statistics"""

        # Filter errors by project if specified
        errors = [
            error for error in self.error_history.values()
            if project_id is None or error.project_id == project_id
        ]

        if not errors:
            return {"total_errors": 0}

        # Calculate statistics
        total_errors = len(errors)
        resolved_errors = sum(1 for error in errors if error.resolved)

        # Category breakdown
        category_counts = {}
        for error in errors:
            category = error.category.value
            category_counts[category] = category_counts.get(category, 0) + 1

        # Severity breakdown
        severity_counts = {}
        for error in errors:
            severity = error.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

        # Pattern matching statistics
        pattern_usage = {}
        for error in errors:
            for pattern_id in error.pattern_matches:
                pattern_usage[pattern_id] = pattern_usage.get(pattern_id, 0) + 1

        # Resolution statistics
        automated_fixes = sum(
            1 for error in errors
            if any(attempt.get("automated", False) for attempt in error.resolution_attempts)
        )

        return {
            "total_errors": total_errors,
            "resolved_errors": resolved_errors,
            "resolution_rate": resolved_errors / total_errors if total_errors > 0 else 0,
            "category_breakdown": category_counts,
            "severity_breakdown": severity_counts,
            "pattern_usage": pattern_usage,
            "automated_fixes_attempted": automated_fixes,
            "pattern_statistics": self.pattern_statistics
        }

    def get_error_trends(self, project_id: Optional[int] = None, days: int = 7) -> Dict[str, Any]:
        """Get error trends over time"""

        cutoff_time = time.time() - (days * 24 * 60 * 60)

        # Filter recent errors
        recent_errors = [
            error for error in self.error_history.values()
            if error.timestamp >= cutoff_time and (project_id is None or error.project_id == project_id)
        ]

        # Group by day
        daily_counts = {}
        for error in recent_errors:
            day = int(error.timestamp // (24 * 60 * 60))
            daily_counts[day] = daily_counts.get(day, 0) + 1

        # Calculate trend
        if len(daily_counts) >= 2:
            days_list = sorted(daily_counts.keys())
            recent_avg = sum(daily_counts[day] for day in days_list[-3:]) / min(3, len(days_list))
            older_avg = sum(daily_counts[day] for day in days_list[:-3]) / max(1, len(days_list) - 3)
            trend = "increasing" if recent_avg > older_avg else "decreasing" if recent_avg < older_avg else "stable"
        else:
            trend = "insufficient_data"

        return {
            "period_days": days,
            "total_errors": len(recent_errors),
            "daily_counts": daily_counts,
            "trend": trend,
            "most_common_category": max(
                (error.category.value for error in recent_errors),
                key=lambda cat: sum(1 for e in recent_errors if e.category.value == cat),
                default="none"
            ) if recent_errors else "none"
        }

    def _generate_error_id(self, message: str, file_path: Optional[str], line_number: Optional[int]) -> str:
        """Generate unique error ID"""
        content = f"{message}:{file_path}:{line_number}"
        return hashlib.md5(content.encode()).hexdigest()[:12]

    def _match_error_patterns(self, error_message: str) -> List[ErrorPattern]:
        """Match error message against known patterns"""
        matched_patterns = []

        for pattern in self.error_patterns.values():
            for regex_pattern in pattern.regex_patterns:
                if re.search(regex_pattern, error_message, re.IGNORECASE):
                    matched_patterns.append(pattern)
                    break

        return matched_patterns

    async def _ai_categorize_error(self, error_message: str) -> Tuple[ErrorCategory, ErrorSeverity]:
        """Use AI to categorize unknown errors"""
        try:
            # Simple categorization based on keywords
            message_lower = error_message.lower()

            # Category detection
            if any(keyword in message_lower for keyword in ['syntax', 'invalid syntax', 'indentation']):
                category = ErrorCategory.SYNTAX
            elif any(keyword in message_lower for keyword in ['import', 'module', 'package']):
                category = ErrorCategory.DEPENDENCY
            elif any(keyword in message_lower for keyword in ['permission', 'access', 'security']):
                category = ErrorCategory.SECURITY
            elif any(keyword in message_lower for keyword in ['performance', 'timeout', 'memory']):
                category = ErrorCategory.PERFORMANCE
            elif any(keyword in message_lower for keyword in ['config', 'setting', 'environment']):
                category = ErrorCategory.CONFIGURATION
            elif any(keyword in message_lower for keyword in ['runtime', 'execution', 'null', 'undefined']):
                category = ErrorCategory.RUNTIME
            else:
                category = ErrorCategory.UNKNOWN

            # Severity detection
            if any(keyword in message_lower for keyword in ['critical', 'fatal', 'crash', 'segmentation']):
                severity = ErrorSeverity.CRITICAL
            elif any(keyword in message_lower for keyword in ['error', 'exception', 'failed']):
                severity = ErrorSeverity.HIGH
            elif any(keyword in message_lower for keyword in ['warning', 'deprecated']):
                severity = ErrorSeverity.MEDIUM
            else:
                severity = ErrorSeverity.LOW

            return category, severity

        except Exception as e:
            logger.error(f"AI categorization failed: {e}")
            return ErrorCategory.UNKNOWN, ErrorSeverity.MEDIUM

    async def _get_ai_error_analysis(self, error_instance: ErrorInstance) -> str:
        """Get AI analysis of the error"""
        try:
            analysis_result = await self.ai_service.debug_error(
                error_instance.project_id,
                error_instance.message,
                error_instance.file_path,
                error_instance.stack_trace
            )

            if analysis_result["success"]:
                return analysis_result["debug_analysis"]
            else:
                return f"AI analysis failed: {analysis_result.get('error', 'Unknown error')}"

        except Exception as e:
            logger.error(f"AI error analysis failed: {e}")
            return f"Analysis unavailable: {str(e)}"

    def _find_related_errors(self, error_instance: ErrorInstance) -> List[str]:
        """Find related errors in history"""
        related = []

        for other_id, other_error in self.error_history.items():
            if other_id == error_instance.error_id:
                continue

            # Check for similarity
            similarity_score = 0

            # Same file
            if error_instance.file_path and other_error.file_path == error_instance.file_path:
                similarity_score += 0.3

            # Same category
            if other_error.category == error_instance.category:
                similarity_score += 0.2

            # Similar message
            if self._calculate_message_similarity(error_instance.message, other_error.message) > 0.5:
                similarity_score += 0.4

            # Same pattern matches
            common_patterns = set(error_instance.pattern_matches) & set(other_error.pattern_matches)
            if common_patterns:
                similarity_score += 0.3

            if similarity_score >= 0.5:
                related.append(other_id)

        return related[:5]  # Top 5 related errors

    def _calculate_message_similarity(self, msg1: str, msg2: str) -> float:
        """Calculate similarity between error messages"""
        words1 = set(msg1.lower().split())
        words2 = set(msg2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1 & words2
        union = words1 | words2

        return len(intersection) / len(union)

    def _generate_suggested_fixes(self, error_instance: ErrorInstance, patterns: List[ErrorPattern]) -> List[str]:
        """Generate suggested fixes based on patterns and analysis"""
        fixes = []

        # Add pattern-based fixes
        for pattern in patterns:
            fixes.extend(pattern.resolution_steps)

        # Add context-specific fixes
        if error_instance.file_path:
            fixes.append(f"Check the code in {error_instance.file_path}")

            if error_instance.line_number:
                fixes.append(f"Review line {error_instance.line_number} in {error_instance.file_path}")

        # Remove duplicates while preserving order
        unique_fixes = []
        seen = set()
        for fix in fixes:
            if fix not in seen:
                unique_fixes.append(fix)
                seen.add(fix)

        return unique_fixes[:10]  # Top 10 suggestions

    async def _check_automated_fix(self, error_instance: ErrorInstance) -> Tuple[bool, List[str]]:
        """Check if automated fix is available"""
        fix_commands = []

        # Pattern-based automated fixes
        for pattern_id in error_instance.pattern_matches:
            pattern = self.error_patterns.get(pattern_id)
            if not pattern:
                continue

            if pattern.category == ErrorCategory.SYNTAX:
                # Syntax errors might be auto-fixable with linters
                if error_instance.file_path:
                    fix_commands.append(f"Run linter on {error_instance.file_path}")

            elif pattern.category == ErrorCategory.DEPENDENCY:
                # Import errors might be auto-fixable
                if "module named" in error_instance.message.lower():
                    # Extract module name
                    import re
                    match = re.search(r"No module named '([^']+)'", error_instance.message)
                    if match:
                        module_name = match.group(1)
                        fix_commands.append(f"pip install {module_name}")

        return len(fix_commands) > 0, fix_commands

    def _calculate_analysis_confidence(
        self,
        error_instance: ErrorInstance,
        patterns: List[ErrorPattern],
        ai_analysis: str
    ) -> float:
        """Calculate confidence in the analysis"""
        confidence = 0.0

        # Pattern matching confidence
        if patterns:
            confidence += 0.4

        # AI analysis confidence
        if ai_analysis and "analysis unavailable" not in ai_analysis.lower():
            confidence += 0.3

        # Context availability
        if error_instance.file_path:
            confidence += 0.1
        if error_instance.line_number:
            confidence += 0.1
        if error_instance.stack_trace:
            confidence += 0.1

        return min(confidence, 1.0)

    def _update_pattern_statistics(self, patterns: List[ErrorPattern]):
        """Update pattern usage statistics"""
        for pattern in patterns:
            if pattern.pattern_id not in self.pattern_statistics:
                self.pattern_statistics[pattern.pattern_id] = {
                    "usage_count": 0,
                    "last_used": None,
                    "success_rate": 0.0
                }

            stats = self.pattern_statistics[pattern.pattern_id]
            stats["usage_count"] += 1
            stats["last_used"] = time.time()
